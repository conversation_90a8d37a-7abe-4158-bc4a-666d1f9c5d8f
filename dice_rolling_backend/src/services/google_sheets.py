"""
Google Sheets integration service for dice rolling history.
This is a mock implementation for demonstration purposes.
In production, you would need to set up Google Sheets API credentials.
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any

class GoogleSheetsService:
    def __init__(self):
        # Mock data storage file (simulating Google Sheets)
        self.data_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'sheets_data.json')
        self.ensure_data_directory()
        
    def ensure_data_directory(self):
        """Ensure the data directory exists"""
        data_dir = os.path.dirname(self.data_file)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            
    def load_data(self) -> List[Dict[str, Any]]:
        """Load existing data from mock storage"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []
    
    def save_data(self, data: List[Dict[str, Any]]):
        """Save data to mock storage"""
        with open(self.data_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def add_dice_roll_record(self, username: str, dice1: int, dice2: int, dice3: int, 
                           total_score: int, timestamp: str = None) -> bool:
        """
        Add a dice roll record to the Google Sheet (mock implementation)
        
        Args:
            username: Player's username
            dice1, dice2, dice3: Individual dice values
            total_score: Sum of all dice
            timestamp: When the roll occurred (ISO format)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if timestamp is None:
                timestamp = datetime.utcnow().isoformat()
                
            # Load existing data
            data = self.load_data()
            
            # Create new record
            new_record = {
                'timestamp': timestamp,
                'username': username,
                'dice1': dice1,
                'dice2': dice2,
                'dice3': dice3,
                'total_score': total_score,
                'date': datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d'),
                'time': datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%H:%M:%S')
            }
            
            # Add to data
            data.append(new_record)
            
            # Save updated data
            self.save_data(data)
            
            return True
            
        except Exception as e:
            return False
    
    def get_all_records(self) -> List[Dict[str, Any]]:
        """Get all dice roll records from the Google Sheet"""
        return self.load_data()
    
    def get_records_by_username(self, username: str) -> List[Dict[str, Any]]:
        """Get all records for a specific username"""
        data = self.load_data()
        return [record for record in data if record.get('username') == username]
    
    def get_leaderboard(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top players by highest score"""
        data = self.load_data()
        
        # Group by username and find highest score for each
        user_scores = {}
        for record in data:
            username = record.get('username')
            score = record.get('total_score', 0)
            
            if username not in user_scores or score > user_scores[username]['highest_score']:
                user_scores[username] = {
                    'username': username,
                    'highest_score': score,
                    'timestamp': record.get('timestamp')
                }
        
        # Sort by highest score and return top results
        leaderboard = sorted(user_scores.values(), key=lambda x: x['highest_score'], reverse=True)
        return leaderboard[:limit]
    
    def clear_all_data(self) -> bool:
        """Clear all data (for testing purposes)"""
        try:
            self.save_data([])
            return True
        except Exception as e:
            return False

# Global instance
sheets_service = GoogleSheetsService()

