#!/usr/bin/env python3
"""
Test script to check if we can access the Google Sheet publicly
"""

import requests

def test_public_access():
    """Test if we can access the Google Sheet via CSV export"""
    SPREADSHEET_ID = '1kiCoNvDBawPpOa2A46EMAHruhcC8LGLf5SCZhX6M4q0'
    CSV_EXPORT_URL = f'https://docs.google.com/spreadsheets/d/{SPREADSHEET_ID}/export?format=csv&gid=0'
    
    print(f"Testing public access to Google Sheet...")
    print(f"URL: {CSV_EXPORT_URL}")
    
    try:
        response = requests.get(CSV_EXPORT_URL, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Successfully accessed the Google Sheet!")
            content = response.text
            print(f"Content length: {len(content)} characters")
            
            # Show first few lines
            lines = content.split('\n')[:5]
            print("\nFirst 5 lines:")
            for i, line in enumerate(lines):
                print(f"{i+1}: {line}")
                
        else:
            print(f"❌ Failed to access sheet. Status: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error accessing sheet: {e}")

if __name__ == "__main__":
    test_public_access()
