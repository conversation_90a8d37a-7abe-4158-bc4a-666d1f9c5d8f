#!/usr/bin/env python3
"""
Test script to verify Google Sheets credentials setup
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_credentials():
    """Test if Google Sheets credentials are properly configured"""
    
    print("🔍 Testing Google Sheets Credentials Setup")
    print("=" * 50)
    
    # Check if credentials file exists
    credentials_path = os.path.join('src', 'credentials', 'service-account.json')
    print(f"\n1. Checking credentials file: {credentials_path}")
    
    if os.path.exists(credentials_path):
        print("✅ Credentials file found!")
        
        # Try to load and validate the credentials
        try:
            import json
            with open(credentials_path, 'r') as f:
                creds_data = json.load(f)
            
            required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
            missing_fields = [field for field in required_fields if field not in creds_data]
            
            if missing_fields:
                print(f"❌ Invalid credentials file. Missing fields: {missing_fields}")
                return False
            
            print(f"✅ Credentials file is valid!")
            print(f"   Project ID: {creds_data.get('project_id')}")
            print(f"   Service Account Email: {creds_data.get('client_email')}")
            
        except Exception as e:
            print(f"❌ Error reading credentials file: {e}")
            return False
            
    else:
        print("❌ Credentials file not found!")
        print("\n📋 Setup Instructions:")
        print("1. Follow the instructions in src/credentials/README.md")
        print("2. Download your service account JSON file")
        print("3. Rename it to 'service-account.json'")
        print("4. Place it in src/credentials/ directory")
        return False
    
    # Test the Google Sheets service
    print("\n2. Testing Google Sheets service...")
    try:
        from src.services.google_sheets import sheets_service
        
        # Try to add a test record
        success = sheets_service.add_dice_roll_record(
            username="test_credentials",
            dice1=1,
            dice2=2,
            dice3=3,
            total_score=6
        )
        
        if success:
            print("✅ Successfully added test record to Google Sheets!")
            
            # Try to read records
            records = sheets_service.get_all_records()
            print(f"✅ Successfully read {len(records)} records from Google Sheets!")
            
            return True
        else:
            print("❌ Failed to add test record to Google Sheets")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Google Sheets service: {e}")
        return False

if __name__ == "__main__":
    success = test_credentials()
    
    if success:
        print("\n🎉 SUCCESS! Google Sheets integration is working!")
        print("You can now roll dice and the data will automatically appear in your Google Sheet.")
    else:
        print("\n❌ Setup incomplete. Please follow the instructions above.")
