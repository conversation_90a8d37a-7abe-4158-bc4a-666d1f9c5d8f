# Dice Rolling Game Application

A full-stack web application for dice rolling with user management, rankings, and Google Sheets integration.

## Features

✅ **Username Entry Dialog** - Users enter their username to start playing
✅ **Three Dice Rolling** - Interactive dice rolling with animations
✅ **One-Time Rolling** - Each user can only roll once (prevents multiple attempts)
✅ **Real-time Rankings** - Top players displayed with monthly highlights
✅ **Google Sheets Integration** - All rolls are saved to Google Sheets for history (mock implementation)
✅ **Responsive Design** - Works on desktop and mobile devices
✅ **Modern UI** - Beautiful gradient design with smooth animations

## Technology Stack

### Backend
- **Flask** - Python web framework
- **SQLAlchemy** - Database ORM
- **SQLite** - Database storage
- **Google Sheets API** - Data synchronization (mock implementation)

### Frontend
- **HTML5/CSS3/JavaScript** - Modern web technologies
- **Font Awesome** - Icons
- **Google Fonts** - Typography
- **CSS Grid/Flexbox** - Responsive layout

## Project Structure

```
dice_rolling_backend/
├── src/
│   ├── models/
│   │   └── user.py              # Database models (User, DiceRoll, Ranking)
│   ├── routes/
│   │   └── user.py              # API endpoints
│   ├── services/
│   │   └── google_sheets.py     # Google Sheets integration
│   ├── static/                  # Frontend files (HTML, CSS, JS)
│   │   ├── index.html           # Main application page
│   │   ├── css/
│   │   │   └── style.css        # Application styles
│   │   └── js/
│   │       └── app.js           # Frontend logic
│   ├── database/                # Database directory
│   │   └── app.db               # SQLite database file (generated on first run)
│   └── main.py                  # Flask application entry point
├── venv/                        # Python virtual environment
├── requirements.txt             # Python dependencies
├── init_db.py                   # Database initialization script
├── test_app.py                  # Script to test API endpoints
└── README.md                    # This file
```

**Important Note on `app.db`:** The `app.db` file is the SQLite database. It is **not** included in the initial project files because it is automatically generated when the Flask application runs for the first time, or when you execute the `init_db.py` script. If you delete it, it will be recreated with an empty schema.

## API Endpoints

### User Management
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user
- `GET /api/users/by-username/<username>` - Get user by username

### Dice Rolling
- `POST /api/dice/roll` - Roll dice for a user
- `GET /api/dice/check/<username>` - Check if user has already rolled
- `GET /api/dice/history` - Get all dice roll history

### Rankings
- `GET /api/rankings` - Get top rankings with monthly highlights

### Google Sheets Integration
- `GET /api/sheets/history` - Get all rolls from Google Sheets
- `GET /api/sheets/leaderboard` - Get leaderboard from Google Sheets
- `GET /api/sheets/user/<username>` - Get user's rolls from Google Sheets

### Utility
- `POST /api/reset` - Reset all data (for testing)

## Monthly Highlighting System

The ranking system includes a monthly highlighting feature where players are highlighted based on their rank and the current month:
- January: 1st place player is highlighted
- February: 2nd place player is highlighted
- March: 3rd place player is highlighted
- And so on...

This creates a rotating spotlight system that gives different players recognition throughout the year.

## Setup Instructions

### Prerequisites
- Python 3.11 or higher
- pip (Python package installer)

### Installation Steps

1. **Download and Extract the Project**
   Download the provided `.zip` file and extract its contents to your desired location.

2. **Navigate to the Project Directory**
   Open your terminal or command prompt and navigate to the `dice_rolling_backend` directory:
   ```bash
   cd path/to/your/extracted/dice_rolling_backend
   ```

3. **Create and Activate Virtual Environment**
   It's highly recommended to use a virtual environment to manage dependencies.
   ```bash
   # Create virtual environment
   python3 -m venv venv
   
   # Activate virtual environment
   # On Linux/Mac:
   source venv/bin/activate
   # On Windows (Command Prompt):
   venv\Scripts\activate.bat
   # On Windows (PowerShell):
   venv\Scripts\Activate.ps1
   ```

4. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

5. **Initialize Database**
   This step will create the `app.db` file inside the `src/database/` directory and set up the necessary tables.
   ```bash
   python init_db.py
   ```

6. **Start the Application**
   ```bash
   python src/main.py
   ```

7. **Access the Application**
   Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

### Quick Start (Alternative Method)

If you already have the virtual environment set up and dependencies installed:

```bash
# Navigate to project directory
cd path/to/your/extracted/dice_rolling_backend

# Activate virtual environment
source venv/bin/activate

# Start the application
python src/main.py
```

The application will be available at `http://localhost:5000`

## How to Use

1. **Enter Username**: When you first visit the application, you'll see a dialog asking for your username. Enter a unique username (2+ characters, letters/numbers/underscores only).

2. **Roll Dice**: Click the "Shake Dice!" button to roll three dice. The dice will animate and show your result.

3. **View Results**: Your total score will be displayed, and you'll see a success message with your individual dice values.

4. **One-Time Restriction**: After rolling, the button will change to "Already Rolled!" and become disabled. Each user can only roll once.

5. **Check Rankings**: The right sidebar shows the top rankings. Players are ranked by their highest score.

6. **Monthly Highlights**: One player in the rankings will be highlighted each month based on the current month and their rank position.

7. **View History**: The bottom section shows recent rolls from all players with timestamps.

## Testing the Application

### Test Different Users
1. Open the application in different browser windows/tabs
2. Use different usernames in each window
3. Roll dice with each user
4. Observe how rankings update in real-time

### Test One-Time Rolling Restriction
1. Enter a username and roll dice
2. Try to roll again - the button should be disabled
3. Refresh the page with the same username - should still show "Already Rolled!"

### Test API Endpoints
You can test the API endpoints directly using curl:

```bash
# Get all users
curl http://localhost:5000/api/users

# Get rankings
curl http://localhost:5000/api/rankings

# Get Google Sheets history
curl http://localhost:5000/api/sheets/history

# Roll dice for a user
curl -X POST http://localhost:5000/api/dice/roll \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser"}'

# Reset all data (for testing)
curl -X POST http://localhost:5000/api/reset
```

## Troubleshooting

### Common Issues

**1. Port Already in Use**
```
Error: Address already in use
```
Solution: Kill any existing Flask processes or use a different port:
```bash
# Kill existing processes
pkill -f "python src/main.py"

# Or modify src/main.py to use a different port
app.run(host='0.0.0.0', port=5001, debug=True)
```

**2. Database Errors**
```
Error: no such column
```
Solution: Reinitialize the database:
```bash
rm -f src/database/app.db
python init_db.py
```

**3. Module Not Found Errors**
```
ModuleNotFoundError: No module named 'flask'
```
Solution: Ensure virtual environment is activated and dependencies are installed:
```bash
source venv/bin/activate
pip install -r requirements.txt
```

**4. Permission Errors**
```
Permission denied
```
Solution: Ensure you have write permissions in the project directory:
```bash
chmod -R 755 dice_rolling_backend
```

### Development Notes

**Database Schema**
- `User`: Stores user information (id, username)
- `DiceRoll`: Stores individual dice rolls (user_id, dice1, dice2, dice3, total_score, timestamp)
- `Ranking`: Stores user rankings (user_id, username, highest_score, total_rolls, last_updated)

**Google Sheets Integration**
The current implementation uses a mock Google Sheets service that stores data in a local JSON file (`src/data/sheets_data.json`). For production use with real Google Sheets:

1. Set up Google Cloud Project
2. Enable Google Sheets API
3. Create service account credentials
4. Replace the mock implementation in `src/services/google_sheets.py`

**CORS Configuration**
The application is configured to allow cross-origin requests from any domain for development purposes. For production, update the CORS settings in `src/routes/user.py` to restrict to specific domains.

## Deployment

### Local Development
The application is ready to run locally using the instructions above.

### Production Deployment
For production deployment, consider:

1. **Environment Variables**: Store sensitive configuration in environment variables
2. **Database**: Use PostgreSQL or MySQL instead of SQLite
3. **Web Server**: Use Gunicorn or uWSGI with Nginx
4. **HTTPS**: Configure SSL certificates
5. **Google Sheets**: Implement real Google Sheets API integration

### Docker Deployment (Optional)
Create a `Dockerfile` for containerized deployment:

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
RUN python init_db.py

EXPOSE 5000
CMD ["python", "src/main.py"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the API documentation
3. Test with the provided curl commands
4. Check browser console for JavaScript errors

---

**Enjoy playing the Dice Rolling Game!** 🎲🎲🎲

