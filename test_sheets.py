#!/usr/bin/env python3
"""
Test script to check Google Sheets integration
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from src.services.google_sheets import sheets_service

def test_sheets_connection():
    """Test the Google Sheets connection"""
    print("Testing Google Sheets integration...")
    
    # Test adding a record
    print("\n1. Testing add_dice_roll_record...")
    success = sheets_service.add_dice_roll_record(
        username="test_user",
        dice1=3,
        dice2=4,
        dice3=5,
        total_score=12
    )
    print(f"Add record result: {'✅ Success' if success else '❌ Failed'}")
    
    # Test getting all records
    print("\n2. Testing get_all_records...")
    try:
        records = sheets_service.get_all_records()
        print(f"✅ Retrieved {len(records)} records")
        if records:
            print("Sample record:", records[-1])  # Show last record
    except Exception as e:
        print(f"❌ Failed to get records: {e}")
    
    # Test leaderboard
    print("\n3. Testing get_leaderboard...")
    try:
        leaderboard = sheets_service.get_leaderboard()
        print(f"✅ Retrieved leaderboard with {len(leaderboard)} players")
        if leaderboard:
            print("Top player:", leaderboard[0])
    except Exception as e:
        print(f"❌ Failed to get leaderboard: {e}")

if __name__ == "__main__":
    test_sheets_connection()
