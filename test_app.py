#!/usr/bin/env python3
"""
Test script for the Dice Rolling Game Application
This script tests all major functionality of the application.
"""

import requests
import json
import time

# Configuration
BASE_URL = 'http://localhost:5000'
API_URL = f'{BASE_URL}/api'

def test_api_endpoint(method, endpoint, data=None, expected_status=200):
    """Test an API endpoint and return the response"""
    url = f'{API_URL}{endpoint}'
    
    try:
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        
        print(f"{method} {endpoint} - Status: {response.status_code}")
        
        if response.status_code == expected_status:
            print("✅ PASS")
            return response.json() if response.content else None
        else:
            print(f"❌ FAIL - Expected {expected_status}, got {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ ERROR - {str(e)}")
        return None

def main():
    """Run all tests"""
    print("🎲 Dice Rolling Game - Application Test Suite")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("\n1. Testing server connectivity...")
    try:
        response = requests.get(BASE_URL)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server not responding correctly")
            return
    except:
        print("❌ Server is not running. Please start with: python src/main.py")
        return
    
    # Test 2: Get initial users (should be empty or have existing users)
    print("\n2. Testing user endpoints...")
    users = test_api_endpoint('GET', '/users')
    print(f"Current users: {len(users) if users else 0}")
    
    # Test 3: Get initial rankings
    print("\n3. Testing rankings endpoint...")
    rankings = test_api_endpoint('GET', '/rankings')
    print(f"Current rankings: {len(rankings) if rankings else 0}")
    
    # Test 4: Test dice rolling with new user
    print("\n4. Testing dice rolling...")
    test_username = f"testuser_{int(time.time())}"
    roll_data = {"username": test_username}
    roll_result = test_api_endpoint('POST', '/dice/roll', roll_data, 201)
    
    if roll_result:
        print(f"Roll result: {roll_result['roll']['dice1']}, {roll_result['roll']['dice2']}, {roll_result['roll']['dice3']} = {roll_result['roll']['total_score']}")
    
    # Test 5: Test one-time rolling restriction
    print("\n5. Testing one-time rolling restriction...")
    duplicate_roll = test_api_endpoint('POST', '/dice/roll', roll_data, 400)
    if duplicate_roll and 'error' in duplicate_roll:
        print(f"✅ Restriction working: {duplicate_roll['error']}")
    
    # Test 6: Check user roll status
    print("\n6. Testing roll status check...")
    status = test_api_endpoint('GET', f'/dice/check/{test_username}')
    if status and status.get('has_rolled'):
        print("✅ Roll status correctly tracked")
    
    # Test 7: Test Google Sheets integration
    print("\n7. Testing Google Sheets integration...")
    sheets_history = test_api_endpoint('GET', '/sheets/history')
    if sheets_history:
        print(f"✅ Google Sheets history: {len(sheets_history)} records")
    
    sheets_leaderboard = test_api_endpoint('GET', '/sheets/leaderboard')
    if sheets_leaderboard:
        print(f"✅ Google Sheets leaderboard: {len(sheets_leaderboard)} players")
    
    # Test 8: Test dice history
    print("\n8. Testing dice history...")
    history = test_api_endpoint('GET', '/dice/history')
    if history:
        print(f"✅ Dice history: {len(history)} rolls")
    
    # Test 9: Test updated rankings
    print("\n9. Testing updated rankings...")
    updated_rankings = test_api_endpoint('GET', '/rankings')
    if updated_rankings:
        print(f"✅ Updated rankings: {len(updated_rankings)} players")
        for i, ranking in enumerate(updated_rankings[:3]):
            highlight = "⭐" if ranking.get('is_highlighted') else ""
            print(f"  {ranking['rank']}. {ranking['username']}: {ranking['highest_score']} {highlight}")
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\nTo test the frontend:")
    print(f"1. Open your browser to {BASE_URL}")
    print("2. Enter a username and click 'Start Game'")
    print("3. Click 'Shake Dice!' to roll")
    print("4. Verify the dice animation and results")
    print("5. Check that rankings and history update")
    print("6. Try rolling again (should be disabled)")

if __name__ == '__main__':
    main()

