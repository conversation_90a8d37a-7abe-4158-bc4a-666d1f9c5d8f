#!/usr/bin/env python3
import os
import sys
sys.path.insert(0, os.path.dirname(__file__))

from dice_rolling_backend.src.models.user import db, User, <PERSON><PERSON><PERSON><PERSON>, Ranking
from dice_rolling_backend.src.main import app

def init_database():
    with app.app_context():
        # Drop all tables
        db.drop_all()
        print("Dropped all tables")
        
        # Create all tables
        db.create_all()
        print("Created all tables")
        
        print("Database initialized successfully!")

if __name__ == '__main__':
    init_database()

