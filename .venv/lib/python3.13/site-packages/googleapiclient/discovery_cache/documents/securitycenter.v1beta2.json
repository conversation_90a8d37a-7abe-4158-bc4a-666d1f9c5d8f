{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://securitycenter.googleapis.com/", "batchPath": "batch", "canonicalName": "Security Command Center", "description": "Security Command Center API provides access to temporal views of assets and findings within an organization.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/security-command-center", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "securitycenter:v1beta2", "kind": "discovery#restDescription", "mtlsRootUrl": "https://securitycenter.mtls.googleapis.com/", "name": "securitycenter", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"folders": {"methods": {"getContainerThreatDetectionSettings": {"description": "Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/containerThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.folders.getContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getEventThreatDetectionSettings": {"description": "Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/eventThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.folders.getEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getRapidVulnerabilityDetectionSettings": {"description": "Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings", "httpMethod": "GET", "id": "securitycenter.folders.getRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to retrieve. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityCenterSettings": {"description": "Get the SecurityCenterSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/securityCenterSettings", "httpMethod": "GET", "id": "securitycenter.folders.getSecurityCenterSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityCenterSettings to retrieve. Format: organizations/{organization}/securityCenterSettings Format: folders/{folder}/securityCenterSettings Format: projects/{project}/securityCenterSettings", "location": "path", "pattern": "^folders/[^/]+/securityCenterSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityCenterSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityHealthAnalyticsSettings": {"description": "Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings", "httpMethod": "GET", "id": "securitycenter.folders.getSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to retrieve. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^folders/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getVirtualMachineThreatDetectionSettings": {"description": "Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.folders.getVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getWebSecurityScannerSettings": {"description": "Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.", "flatPath": "v1beta2/folders/{foldersId}/webSecurityScannerSettings", "httpMethod": "GET", "id": "securitycenter.folders.getWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to retrieve. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^folders/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateContainerThreatDetectionSettings": {"description": "Update the ContainerThreatDetectionSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/containerThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ContainerThreatDetectionSettings. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "ContainerThreatDetectionSettings"}, "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateEventThreatDetectionSettings": {"description": "Update the EventThreatDetectionSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/eventThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the EventThreatDetectionSettings. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "EventThreatDetectionSettings"}, "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateRapidVulnerabilityDetectionSettings": {"description": "Update the RapidVulnerabilityDetectionSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the RapidVulnerabilityDetectionSettings. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "RapidVulnerabilityDetectionSettings"}, "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSecurityHealthAnalyticsSettings": {"description": "Update the SecurityHealthAnalyticsSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the SecurityHealthAnalyticsSettings. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^folders/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "SecurityHealthAnalyticsSettings"}, "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateVirtualMachineThreatDetectionSettings": {"description": "Update the VirtualMachineThreatDetectionSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the VirtualMachineThreatDetectionSettings. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "VirtualMachineThreatDetectionSettings"}, "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateWebSecurityScannerSettings": {"description": "Update the WebSecurityScannerSettings resource.", "flatPath": "v1beta2/folders/{foldersId}/webSecurityScannerSettings", "httpMethod": "PATCH", "id": "securitycenter.folders.updateWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebSecurityScannerSettings. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^folders/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "WebSecurityScannerSettings"}, "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"containerThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/containerThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.containerThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to calculate. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "eventThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/eventThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.eventThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to calculate. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rapidVulnerabilityDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/rapidVulnerabilityDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.rapidVulnerabilityDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to calculate. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityHealthAnalyticsSettings": {"methods": {"calculate": {"description": "Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/securityHealthAnalyticsSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.securityHealthAnalyticsSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to calculate. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^folders/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "virtualMachineThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/virtualMachineThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.virtualMachineThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to calculate. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^folders/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "webSecurityScannerSettings": {"methods": {"calculate": {"description": "Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/folders/{foldersId}/webSecurityScannerSettings:calculate", "httpMethod": "GET", "id": "securitycenter.folders.webSecurityScannerSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to calculate. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^folders/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "organizations": {"methods": {"getContainerThreatDetectionSettings": {"description": "Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getEventThreatDetectionSettings": {"description": "Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getRapidVulnerabilityDetectionSettings": {"description": "Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to retrieve. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityCenterSettings": {"description": "Get the SecurityCenterSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/securityCenterSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getSecurityCenterSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityCenterSettings to retrieve. Format: organizations/{organization}/securityCenterSettings Format: folders/{folder}/securityCenterSettings Format: projects/{project}/securityCenterSettings", "location": "path", "pattern": "^organizations/[^/]+/securityCenterSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityCenterSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityHealthAnalyticsSettings": {"description": "Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to retrieve. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^organizations/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSubscription": {"description": "Get the Subscription resource.", "flatPath": "v1beta2/organizations/{organizationsId}/subscription", "httpMethod": "GET", "id": "securitycenter.organizations.getSubscription", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the subscription to retrieve. Format: organizations/{organization}/subscription", "location": "path", "pattern": "^organizations/[^/]+/subscription$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "Subscription"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getVirtualMachineThreatDetectionSettings": {"description": "Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getWebSecurityScannerSettings": {"description": "Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.", "flatPath": "v1beta2/organizations/{organizationsId}/webSecurityScannerSettings", "httpMethod": "GET", "id": "securitycenter.organizations.getWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to retrieve. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^organizations/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateContainerThreatDetectionSettings": {"description": "Update the ContainerThreatDetectionSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ContainerThreatDetectionSettings. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "ContainerThreatDetectionSettings"}, "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateEventThreatDetectionSettings": {"description": "Update the EventThreatDetectionSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the EventThreatDetectionSettings. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "EventThreatDetectionSettings"}, "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateRapidVulnerabilityDetectionSettings": {"description": "Update the RapidVulnerabilityDetectionSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the RapidVulnerabilityDetectionSettings. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "RapidVulnerabilityDetectionSettings"}, "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSecurityHealthAnalyticsSettings": {"description": "Update the SecurityHealthAnalyticsSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the SecurityHealthAnalyticsSettings. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^organizations/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "SecurityHealthAnalyticsSettings"}, "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateVirtualMachineThreatDetectionSettings": {"description": "Update the VirtualMachineThreatDetectionSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the VirtualMachineThreatDetectionSettings. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "VirtualMachineThreatDetectionSettings"}, "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateWebSecurityScannerSettings": {"description": "Update the WebSecurityScannerSettings resource.", "flatPath": "v1beta2/organizations/{organizationsId}/webSecurityScannerSettings", "httpMethod": "PATCH", "id": "securitycenter.organizations.updateWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebSecurityScannerSettings. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^organizations/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "WebSecurityScannerSettings"}, "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"containerThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/containerThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.containerThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to calculate. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "eventThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/eventThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.eventThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to calculate. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rapidVulnerabilityDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/rapidVulnerabilityDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.rapidVulnerabilityDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to calculate. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityHealthAnalyticsSettings": {"methods": {"calculate": {"description": "Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/securityHealthAnalyticsSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.securityHealthAnalyticsSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to calculate. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^organizations/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "virtualMachineThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/virtualMachineThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.virtualMachineThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to calculate. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^organizations/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "webSecurityScannerSettings": {"methods": {"calculate": {"description": "Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/organizations/{organizationsId}/webSecurityScannerSettings:calculate", "httpMethod": "GET", "id": "securitycenter.organizations.webSecurityScannerSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to calculate. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^organizations/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "projects": {"methods": {"getContainerThreatDetectionSettings": {"description": "Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/containerThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.projects.getContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getEventThreatDetectionSettings": {"description": "Get the EventThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetEventThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateEventThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/eventThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.projects.getEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getRapidVulnerabilityDetectionSettings": {"description": "Get the RapidVulnerabilityDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetRapidVulnerabilityDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateRapidVulnerabilityDetectionSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings", "httpMethod": "GET", "id": "securitycenter.projects.getRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to retrieve. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityCenterSettings": {"description": "Get the SecurityCenterSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/securityCenterSettings", "httpMethod": "GET", "id": "securitycenter.projects.getSecurityCenterSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityCenterSettings to retrieve. Format: organizations/{organization}/securityCenterSettings Format: folders/{folder}/securityCenterSettings Format: projects/{project}/securityCenterSettings", "location": "path", "pattern": "^projects/[^/]+/securityCenterSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityCenterSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getSecurityHealthAnalyticsSettings": {"description": "Get the SecurityHealthAnalyticsSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetSecurityHealthAnalyticsSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateSecurityHealthAnalyticsSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings", "httpMethod": "GET", "id": "securitycenter.projects.getSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to retrieve. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^projects/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getVirtualMachineThreatDetectionSettings": {"description": "Get the VirtualMachineThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetVirtualMachineThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateVirtualMachineThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.projects.getVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getWebSecurityScannerSettings": {"description": "Get the WebSecurityScannerSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetWebSecurityScannerSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateWebSecurityScannerSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/webSecurityScannerSettings", "httpMethod": "GET", "id": "securitycenter.projects.getWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to retrieve. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^projects/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateContainerThreatDetectionSettings": {"description": "Update the ContainerThreatDetectionSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/containerThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ContainerThreatDetectionSettings. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "ContainerThreatDetectionSettings"}, "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateEventThreatDetectionSettings": {"description": "Update the EventThreatDetectionSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/eventThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateEventThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the EventThreatDetectionSettings. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "EventThreatDetectionSettings"}, "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateRapidVulnerabilityDetectionSettings": {"description": "Update the RapidVulnerabilityDetectionSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateRapidVulnerabilityDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the RapidVulnerabilityDetectionSettings. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "RapidVulnerabilityDetectionSettings"}, "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateSecurityHealthAnalyticsSettings": {"description": "Update the SecurityHealthAnalyticsSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateSecurityHealthAnalyticsSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the SecurityHealthAnalyticsSettings. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^projects/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "SecurityHealthAnalyticsSettings"}, "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateVirtualMachineThreatDetectionSettings": {"description": "Update the VirtualMachineThreatDetectionSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateVirtualMachineThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the VirtualMachineThreatDetectionSettings. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "VirtualMachineThreatDetectionSettings"}, "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateWebSecurityScannerSettings": {"description": "Update the WebSecurityScannerSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/webSecurityScannerSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.updateWebSecurityScannerSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the WebSecurityScannerSettings. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^projects/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "WebSecurityScannerSettings"}, "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"containerThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/containerThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.containerThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to calculate. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "eventThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective EventThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/eventThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.eventThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the EventThreatDetectionSettings to calculate. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/eventThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "EventThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "locations": {"resources": {"clusters": {"methods": {"getContainerThreatDetectionSettings": {"description": "Get the ContainerThreatDetectionSettings resource. In the returned settings response, a missing field only indicates that it was not explicitly set, so no assumption should be made about these fields. In other words, GetContainerThreatDetectionSettings does not calculate the effective service settings for the resource, which accounts for inherited settings and defaults. Instead, use CalculateContainerThreatDetectionSettings for this purpose.", "flatPath": "v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings", "httpMethod": "GET", "id": "securitycenter.projects.locations.clusters.getContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to retrieve. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "updateContainerThreatDetectionSettings": {"description": "Update the ContainerThreatDetectionSettings resource.", "flatPath": "v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings", "httpMethod": "PATCH", "id": "securitycenter.projects.locations.clusters.updateContainerThreatDetectionSettings", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name of the ContainerThreatDetectionSettings. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}, "updateMask": {"description": "The list of fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta2/{+name}", "request": {"$ref": "ContainerThreatDetectionSettings"}, "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"containerThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective ContainerThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/locations/{locationsId}/clusters/{clustersId}/containerThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.locations.clusters.containerThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the ContainerThreatDetectionSettings to calculate. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/clusters/[^/]+/containerThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "ContainerThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "rapidVulnerabilityDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective RapidVulnerabilityDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/rapidVulnerabilityDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.rapidVulnerabilityDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the RapidVulnerabilityDetectionSettings to calculate. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/rapidVulnerabilityDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "RapidVulnerabilityDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityHealthAnalyticsSettings": {"methods": {"calculate": {"description": "Calculates the effective SecurityHealthAnalyticsSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/securityHealthAnalyticsSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.securityHealthAnalyticsSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the SecurityHealthAnalyticsSettings to calculate. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "location": "path", "pattern": "^projects/[^/]+/securityHealthAnalyticsSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "SecurityHealthAnalyticsSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "virtualMachineThreatDetectionSettings": {"methods": {"calculate": {"description": "Calculates the effective VirtualMachineThreatDetectionSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/virtualMachineThreatDetectionSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.virtualMachineThreatDetectionSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the VirtualMachineThreatDetectionSettings to calculate. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "location": "path", "pattern": "^projects/[^/]+/virtualMachineThreatDetectionSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "VirtualMachineThreatDetectionSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "webSecurityScannerSettings": {"methods": {"calculate": {"description": "Calculates the effective WebSecurityScannerSettings based on its level in the resource hierarchy and its settings. Settings provided closer to the target resource take precedence over those further away (e.g. folder will override organization level settings). The default SCC setting for the detector service defaults can be overridden at organization, folder and project levels. No assumptions should be made about the SCC defaults as it is considered an internal implementation detail.", "flatPath": "v1beta2/projects/{projectsId}/webSecurityScannerSettings:calculate", "httpMethod": "GET", "id": "securitycenter.projects.webSecurityScannerSettings.calculate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the WebSecurityScannerSettings to calculate. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "location": "path", "pattern": "^projects/[^/]+/webSecurityScannerSettings$", "required": true, "type": "string"}}, "path": "v1beta2/{+name}:calculate", "response": {"$ref": "WebSecurityScannerSettings"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "********", "rootUrl": "https://securitycenter.googleapis.com/", "schemas": {"Access": {"description": "Represents an access event.", "id": "Access", "properties": {"callerIp": {"description": "Caller's IP address, such as \"*******\".", "type": "string"}, "callerIpGeo": {"$ref": "Geolocation", "description": "The caller IP's geolocation, which identifies where the call came from."}, "methodName": {"description": "The method that the service account called, e.g. \"SetIamPolicy\".", "type": "string"}, "principalEmail": {"description": "Associated email, such as \"<EMAIL>\". The email address of the authenticated user or a service account acting on behalf of a third party principal making the request. For third party identity callers, the `principal_subject` field is populated instead of this field. For privacy reasons, the principal email address is sometimes redacted. For more information, see [Caller identities in audit logs](https://cloud.google.com/logging/docs/audit#user-id).", "type": "string"}, "principalSubject": {"description": "A string that represents the principal_subject that is associated with the identity. Unlike `principal_email`, `principal_subject` supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format is `principal://iam.googleapis.com/{identity pool name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD, FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format `serviceAccount:{identity pool name}[{subject}]`.", "type": "string"}, "serviceAccountDelegationInfo": {"description": "The identity delegation history of an authenticated service account that made the request. The `serviceAccountDelegationInfo[]` object contains information about the real authorities that try to access Google Cloud resources by delegating on a service account. When multiple authorities are present, they are guaranteed to be sorted based on the original ordering of the identity delegation events.", "items": {"$ref": "ServiceAccountDelegationInfo"}, "type": "array"}, "serviceAccountKeyName": {"description": "The name of the service account key that was used to create or exchange credentials when authenticating the service account that made the request. This is a scheme-less URI full resource name. For example: \"//iam.googleapis.com/projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}\". ", "type": "string"}, "serviceName": {"description": "This is the API service that the service account made a call to, e.g. \"iam.googleapis.com\"", "type": "string"}, "userAgent": {"description": "The caller's user agent string associated with the finding.", "type": "string"}, "userAgentFamily": {"description": "Type of user agent associated with the finding. For example, an operating system shell or an embedded or standalone application.", "type": "string"}, "userName": {"description": "A string that represents a username. The username provided depends on the type of the finding and is likely not an IAM principal. For example, this can be a system username if the finding is related to a virtual machine, or it can be an application login username.", "type": "string"}}, "type": "object"}, "AccessReview": {"description": "Conveys information about a Kubernetes access review (such as one returned by a [`kubectl auth can-i`](https://kubernetes.io/docs/reference/access-authn-authz/authorization/#checking-api-access) command) that was involved in a finding.", "id": "AccessReview", "properties": {"group": {"description": "The API group of the resource. \"*\" means all.", "type": "string"}, "name": {"description": "The name of the resource being requested. Empty means all.", "type": "string"}, "ns": {"description": "Namespace of the action being requested. Currently, there is no distinction between no namespace and all namespaces. Both are represented by \"\" (empty).", "type": "string"}, "resource": {"description": "The optional resource type requested. \"*\" means all.", "type": "string"}, "subresource": {"description": "The optional subresource type.", "type": "string"}, "verb": {"description": "A Kubernetes resource API verb, like get, list, watch, create, update, delete, proxy. \"*\" means all.", "type": "string"}, "version": {"description": "The API version of the resource. \"*\" means all.", "type": "string"}}, "type": "object"}, "AttackExposure": {"description": "An attack exposure contains the results of an attack path simulation run.", "id": "AttackExposure", "properties": {"attackExposureResult": {"description": "The resource name of the attack path simulation result that contains the details regarding this attack exposure score. Example: organizations/123/attackExposureResults/456", "type": "string"}, "exposedHighValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedLowValueResourcesCount": {"description": "The number of high value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "exposedMediumValueResourcesCount": {"description": "The number of medium value resources that are exposed as a result of this finding.", "format": "int32", "type": "integer"}, "latestCalculationTime": {"description": "The most recent time the attack exposure was updated on this finding.", "format": "google-datetime", "type": "string"}, "score": {"description": "A number between 0 (inclusive) and infinity that represents how important this finding is to remediate. The higher the score, the more important it is to remediate.", "format": "double", "type": "number"}, "state": {"description": "What state this AttackExposure is in. This captures whether or not an attack exposure has been calculated or not.", "enum": ["STATE_UNSPECIFIED", "CALCULATED", "NOT_CALCULATED"], "enumDescriptions": ["The state is not specified.", "The attack exposure has been calculated.", "The attack exposure has not been calculated."], "type": "string"}}, "type": "object"}, "CloudDlpDataProfile": {"description": "The [data profile](https://cloud.google.com/dlp/docs/data-profiles) associated with the finding.", "id": "CloudDlpDataProfile", "properties": {"dataProfile": {"description": "Name of the data profile, for example, `projects/123/locations/europe/tableProfiles/8383929`.", "type": "string"}, "parentType": {"description": "The resource hierarchy level at which the data profile was generated.", "enum": ["PARENT_TYPE_UNSPECIFIED", "ORGANIZATION", "PROJECT"], "enumDescriptions": ["Unspecified parent type.", "Organization-level configurations.", "Project-level configurations."], "type": "string"}}, "type": "object"}, "CloudDlpInspection": {"description": "Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced the finding.", "id": "CloudDlpInspection", "properties": {"fullScan": {"description": "Whether Cloud DLP scanned the complete resource or a sampled subset.", "type": "boolean"}, "infoType": {"description": "The type of information (or *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*) found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.", "type": "string"}, "infoTypeCount": {"description": "The number of times Cloud DLP found this infoType within this job and resource.", "format": "int64", "type": "string"}, "inspectJob": {"description": "Name of the inspection job, for example, `projects/123/locations/europe/dlpJobs/i-8383929`.", "type": "string"}}, "type": "object"}, "CloudLoggingEntry": {"description": "Metadata taken from a [Cloud Logging LogEntry](https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry)", "id": "CloudLoggingEntry", "properties": {"insertId": {"description": "A unique identifier for the log entry.", "type": "string"}, "logId": {"description": "The type of the log (part of `log_name`. `log_name` is the resource name of the log to which this log entry belongs). For example: `cloudresourcemanager.googleapis.com/activity`. Note that this field is not URL-encoded, unlike the `LOG_ID` field in `LogEntry`.", "type": "string"}, "resourceContainer": {"description": "The organization, folder, or project of the monitored resource that produced this log entry.", "type": "string"}, "timestamp": {"description": "The time the event described by the log entry occurred.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Compliance": {"description": "Contains compliance information about a security standard indicating unmet recommendations.", "id": "Compliance", "properties": {"ids": {"description": "Policies within the standard or benchmark, for example, A.12.4.1", "items": {"type": "string"}, "type": "array"}, "standard": {"description": "Industry-wide compliance standards or benchmarks, such as CIS, PCI, and OWASP.", "type": "string"}, "version": {"description": "Version of the standard or benchmark, for example, 1.1", "type": "string"}}, "type": "object"}, "Config": {"description": "Configuration of a module.", "id": "Config", "properties": {"moduleEnablementState": {"description": "The state of enablement for the module at its level of the resource hierarchy.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "value": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The configuration value for the module. The absence of this field implies its inheritance from the parent.", "type": "object"}}, "type": "object"}, "Connection": {"description": "Contains information about the IP connection associated with the finding.", "id": "Connection", "properties": {"destinationIp": {"description": "Destination IP address. Not present for sockets that are listening and not connected.", "type": "string"}, "destinationPort": {"description": "Destination port. Not present for sockets that are listening and not connected.", "format": "int32", "type": "integer"}, "protocol": {"description": "IANA Internet Protocol Number such as TCP(6) and UDP(17).", "enum": ["PROTOCOL_UNSPECIFIED", "ICMP", "TCP", "UDP", "GRE", "ESP"], "enumDescriptions": ["Unspecified protocol (not HOPOPT).", "Internet Control Message Protocol.", "Transmission Control Protocol.", "User Datagram Protocol.", "Generic Routing Encapsulation.", "Encap Security Payload."], "type": "string"}, "sourceIp": {"description": "Source IP address.", "type": "string"}, "sourcePort": {"description": "Source port.", "format": "int32", "type": "integer"}}, "type": "object"}, "Contact": {"description": "The email address of a contact.", "id": "Contact", "properties": {"email": {"description": "An email address. For example, \"`<EMAIL>`\".", "type": "string"}}, "type": "object"}, "ContactDetails": {"description": "Details about specific contacts", "id": "ContactDetails", "properties": {"contacts": {"description": "A list of contacts", "items": {"$ref": "Contact"}, "type": "array"}}, "type": "object"}, "Container": {"description": "Container associated with the finding.", "id": "Container", "properties": {"createTime": {"description": "The time that the container was created.", "format": "google-datetime", "type": "string"}, "imageId": {"description": "Optional container image ID, if provided by the container runtime. Uniquely identifies the container image launched using a container image digest.", "type": "string"}, "labels": {"description": "Container labels, as provided by the container runtime.", "items": {"$ref": "Label"}, "type": "array"}, "name": {"description": "Name of the container.", "type": "string"}, "uri": {"description": "Container image URI provided when configuring a pod or container. This string can identify a container image version using mutable tags.", "type": "string"}}, "type": "object"}, "ContainerThreatDetectionSettings": {"description": "Resource capturing the settings for the Container Threat Detection service.", "id": "ContainerThreatDetectionSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the ContainerThreatDetectionSettings. Formats: * organizations/{organization}/containerThreatDetectionSettings * folders/{folder}/containerThreatDetectionSettings * projects/{project}/containerThreatDetectionSettings * projects/{project}/locations/{location}/clusters/{cluster}/containerThreatDetectionSettings", "type": "string"}, "serviceAccount": {"description": "Output only. The service account used by Container Threat Detection for scanning. Service accounts are scoped at the project level meaning this field will be empty at any level above a project.", "readOnly": true, "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Cve": {"description": "CVE stands for Common Vulnerabilities and Exposures. More information: https://cve.mitre.org", "id": "Cve", "properties": {"cvssv3": {"$ref": "Cvssv3", "description": "Describe Common Vulnerability Scoring System specified at https://www.first.org/cvss/v3.1/specification-document"}, "id": {"description": "The unique identifier for the vulnerability. e.g. CVE-2021-34527", "type": "string"}, "references": {"description": "Additional information about the CVE. e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527", "items": {"$ref": "Reference"}, "type": "array"}, "upstreamFixAvailable": {"description": "Whether upstream fix is available for the CVE.", "type": "boolean"}}, "type": "object"}, "Cvssv3": {"description": "Common Vulnerability Scoring System version 3.", "id": "Cvssv3", "properties": {"attackComplexity": {"description": "This metric describes the conditions beyond the attacker's control that must exist in order to exploit the vulnerability.", "enum": ["ATTACK_COMPLEXITY_UNSPECIFIED", "ATTACK_COMPLEXITY_LOW", "ATTACK_COMPLEXITY_HIGH"], "enumDescriptions": ["Invalid value.", "Specialized access conditions or extenuating circumstances do not exist. An attacker can expect repeatable success when attacking the vulnerable component.", "A successful attack depends on conditions beyond the attacker's control. That is, a successful attack cannot be accomplished at will, but requires the attacker to invest in some measurable amount of effort in preparation or execution against the vulnerable component before a successful attack can be expected."], "type": "string"}, "attackVector": {"description": "Base Metrics Represents the intrinsic characteristics of a vulnerability that are constant over time and across user environments. This metric reflects the context by which vulnerability exploitation is possible.", "enum": ["ATTACK_VECTOR_UNSPECIFIED", "ATTACK_VECTOR_NETWORK", "ATTACK_VECTOR_ADJACENT", "ATTACK_VECTOR_LOCAL", "ATTACK_VECTOR_PHYSICAL"], "enumDescriptions": ["Invalid value.", "The vulnerable component is bound to the network stack and the set of possible attackers extends beyond the other options listed below, up to and including the entire Internet.", "The vulnerable component is bound to the network stack, but the attack is limited at the protocol level to a logically adjacent topology.", "The vulnerable component is not bound to the network stack and the attacker's path is via read/write/execute capabilities.", "The attack requires the attacker to physically touch or manipulate the vulnerable component."], "type": "string"}, "availabilityImpact": {"description": "This metric measures the impact to the availability of the impacted component resulting from a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "baseScore": {"description": "The base score is a function of the base metric scores.", "format": "double", "type": "number"}, "confidentialityImpact": {"description": "This metric measures the impact to the confidentiality of the information resources managed by a software component due to a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "integrityImpact": {"description": "This metric measures the impact to integrity of a successfully exploited vulnerability.", "enum": ["IMPACT_UNSPECIFIED", "IMPACT_HIGH", "IMPACT_LOW", "IMPACT_NONE"], "enumDescriptions": ["Invalid value.", "High impact.", "Low impact.", "No impact."], "type": "string"}, "privilegesRequired": {"description": "This metric describes the level of privileges an attacker must possess before successfully exploiting the vulnerability.", "enum": ["PRIVILEGES_REQUIRED_UNSPECIFIED", "PRIVILEGES_REQUIRED_NONE", "PRIVILEGES_REQUIRED_LOW", "PRIVILEGES_REQUIRED_HIGH"], "enumDescriptions": ["Invalid value.", "The attacker is unauthorized prior to attack, and therefore does not require any access to settings or files of the vulnerable system to carry out an attack.", "The attacker requires privileges that provide basic user capabilities that could normally affect only settings and files owned by a user. Alternatively, an attacker with Low privileges has the ability to access only non-sensitive resources.", "The attacker requires privileges that provide significant (e.g., administrative) control over the vulnerable component allowing access to component-wide settings and files."], "type": "string"}, "scope": {"description": "The Scope metric captures whether a vulnerability in one vulnerable component impacts resources in components beyond its security scope.", "enum": ["SCOPE_UNSPECIFIED", "SCOPE_UNCHANGED", "SCOPE_CHANGED"], "enumDescriptions": ["Invalid value.", "An exploited vulnerability can only affect resources managed by the same security authority.", "An exploited vulnerability can affect resources beyond the security scope managed by the security authority of the vulnerable component."], "type": "string"}, "userInteraction": {"description": "This metric captures the requirement for a human user, other than the attacker, to participate in the successful compromise of the vulnerable component.", "enum": ["USER_INTERACTION_UNSPECIFIED", "USER_INTERACTION_NONE", "USER_INTERACTION_REQUIRED"], "enumDescriptions": ["Invalid value.", "The vulnerable system can be exploited without interaction from any user.", "Successful exploitation of this vulnerability requires a user to take some action before the vulnerability can be exploited."], "type": "string"}}, "type": "object"}, "Database": {"description": "Represents database access information, such as queries. A database may be a sub-resource of an instance (as in the case of Cloud SQL instances or Cloud Spanner instances), or the database instance itself. Some database resources might not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types, such as Cloud SQL databases, are not yet supported by Cloud Asset Inventory. In these cases only the display name is provided.", "id": "Database", "properties": {"displayName": {"description": "The human-readable name of the database that the user connected to.", "type": "string"}, "grantees": {"description": "The target usernames, roles, or groups of an SQL privilege grant, which is not an IAM policy change.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "Some database resources may not have the [full resource name](https://google.aip.dev/122#full-resource-names) populated because these resource types are not yet supported by Cloud Asset Inventory (e.g. Cloud SQL databases). In these cases only the display name will be provided. The [full resource name](https://google.aip.dev/122#full-resource-names) of the database that the user connected to, if it is supported by Cloud Asset Inventory.", "type": "string"}, "query": {"description": "The SQL statement that is associated with the database access.", "type": "string"}, "userName": {"description": "The username used to connect to the database. The username might not be an IAM principal and does not have a set format.", "type": "string"}, "version": {"description": "The version of the database, for example, POSTGRES_14. See [the complete list](https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).", "type": "string"}}, "type": "object"}, "Details": {"description": "Details of a subscription.", "id": "Details", "properties": {"endTime": {"description": "The time the subscription has or will end.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "The time the subscription has or will start.", "format": "google-datetime", "type": "string"}, "type": {"description": "The type of subscription", "enum": ["TYPE_UNSPECIFIED", "STANDARD", "TRIAL", "ALPHA", "DEMO", "PAY_AS_YOU_GO"], "enumDescriptions": ["Default value. This value is unused.", "The standard subscription.", "The trial subscription.", "The alpha subscription.", "The demo subscription for channel partners.", "Pay-as-you-go subscription."], "type": "string"}}, "type": "object"}, "Detection": {"description": "Memory hash detection contributing to the binary family match.", "id": "Detection", "properties": {"binary": {"description": "The name of the binary associated with the memory hash signature detection.", "type": "string"}, "percentPagesMatched": {"description": "The percentage of memory page hashes in the signature that were matched.", "format": "double", "type": "number"}}, "type": "object"}, "EnvironmentVariable": {"description": "A name-value pair representing an environment variable used in an operating system process.", "id": "EnvironmentVariable", "properties": {"name": {"description": "Environment variable name as a JSON encoded string.", "type": "string"}, "val": {"description": "Environment variable value as a JSON encoded string.", "type": "string"}}, "type": "object"}, "EventThreatDetectionSettings": {"description": "Resource capturing the settings for the Event Threat Detection service.", "id": "EventThreatDetectionSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the EventThreatDetectionSettings. Formats: * organizations/{organization}/eventThreatDetectionSettings * folders/{folder}/eventThreatDetectionSettings * projects/{project}/eventThreatDetectionSettings", "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ExfilResource": {"description": "Resource where data was exfiltrated from or exfiltrated to.", "id": "ExfilResource", "properties": {"components": {"description": "Subcomponents of the asset that was exfiltrated, like URIs used during exfiltration, table names, databases, and filenames. For example, multiple tables might have been exfiltrated from the same Cloud SQL instance, or multiple files might have been exfiltrated from the same Cloud Storage bucket.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "The resource's [full resource name](https://cloud.google.com/apis/design/resource_names#full_resource_name).", "type": "string"}}, "type": "object"}, "Exfiltration": {"description": "Exfiltration represents a data exfiltration attempt from one or more sources to one or more targets. The `sources` attribute lists the sources of the exfiltrated data. The `targets` attribute lists the destinations the data was copied to.", "id": "Exfiltration", "properties": {"sources": {"description": "If there are multiple sources, then the data is considered \"joined\" between them. For instance, BigQuery can join multiple tables, and each table would be considered a source.", "items": {"$ref": "ExfilResource"}, "type": "array"}, "targets": {"description": "If there are multiple targets, each target would get a complete copy of the \"joined\" source data.", "items": {"$ref": "ExfilResource"}, "type": "array"}, "totalExfiltratedBytes": {"description": "Total exfiltrated bytes processed for the entire job.", "format": "int64", "type": "string"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "File": {"description": "File information about the related binary/library used by an executable, or the script used by a script interpreter", "id": "File", "properties": {"contents": {"description": "Prefix of the file contents as a JSON-encoded string.", "type": "string"}, "hashedSize": {"description": "The length in bytes of the file prefix that was hashed. If hashed_size == size, any hashes reported represent the entire file.", "format": "int64", "type": "string"}, "partiallyHashed": {"description": "True when the hash covers only a prefix of the file.", "type": "boolean"}, "path": {"description": "Absolute path of the file as a JSON encoded string.", "type": "string"}, "sha256": {"description": "SHA256 hash of the first hashed_size bytes of the file encoded as a hex string. If hashed_size == size, sha256 represents the SHA256 hash of the entire file.", "type": "string"}, "size": {"description": "Size of the file in bytes.", "format": "int64", "type": "string"}}, "type": "object"}, "Finding": {"description": "Security Command Center finding. A finding is a record of assessment data like security, risk, health, or privacy, that is ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, a cross-site scripting (XSS) vulnerability in an App Engine application is a finding.", "id": "Finding", "properties": {"access": {"$ref": "Access", "description": "Access details associated with the finding, such as more information on the caller, which method was accessed, and from where."}, "attackExposure": {"$ref": "AttackExposure", "description": "The results of an attack path simulation relevant to this finding."}, "canonicalName": {"description": "The canonical name of the finding. It's either \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\" or \"projects/{project_number}/sources/{source_id}/findings/{finding_id}\", depending on the closest CRM ancestor of the resource associated with the finding.", "type": "string"}, "category": {"description": "The additional taxonomy group within findings from a given source. This field is immutable after creation time. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "cloudDlpDataProfile": {"$ref": "CloudDlpDataProfile", "description": "Cloud DLP data profile that is associated with the finding."}, "cloudDlpInspection": {"$ref": "CloudDlpInspection", "description": "Cloud Data Loss Prevention (Cloud DLP) inspection results that are associated with the finding."}, "compliances": {"description": "Contains compliance information for security standards associated to the finding.", "items": {"$ref": "Compliance"}, "type": "array"}, "connections": {"description": "Contains information about the IP connection associated with the finding.", "items": {"$ref": "Connection"}, "type": "array"}, "contacts": {"additionalProperties": {"$ref": "ContactDetails"}, "description": "Output only. Map containing the points of contact for the given finding. The key represents the type of contact, while the value contains a list of all the contacts that pertain. Please refer to: https://cloud.google.com/resource-manager/docs/managing-notification-contacts#notification-categories { \"security\": { \"contacts\": [ { \"email\": \"<EMAIL>\" }, { \"email\": \"<EMAIL>\" } ] } }", "readOnly": true, "type": "object"}, "containers": {"description": "Containers associated with the finding. This field provides information for both Kubernetes and non-Kubernetes containers.", "items": {"$ref": "Container"}, "type": "array"}, "createTime": {"description": "The time at which the finding was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "database": {"$ref": "Database", "description": "Database associated with the finding."}, "description": {"description": "Contains more details about the finding.", "type": "string"}, "eventTime": {"description": "The time the finding was first detected. If an existing finding is updated, then this is the time the update occurred. For example, if the finding represents an open firewall, this property captures the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding is later resolved, then this time reflects when the finding was resolved. This must not be set to a value greater than the current timestamp.", "format": "google-datetime", "type": "string"}, "exfiltration": {"$ref": "Exfiltration", "description": "Represents exfiltrations associated with the finding."}, "externalSystems": {"additionalProperties": {"$ref": "GoogleCloudSecuritycenterV1ExternalSystem"}, "description": "Output only. Third party SIEM/SOAR fields within SCC, contains external system information and external system finding fields.", "readOnly": true, "type": "object"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "files": {"description": "File associated with the finding.", "items": {"$ref": "File"}, "type": "array"}, "findingClass": {"description": "The class of the finding.", "enum": ["FINDING_CLASS_UNSPECIFIED", "THREAT", "VULNERABILITY", "MISCONFIGURATION", "OBSERVATION", "SCC_ERROR", "POSTURE_VIOLATION"], "enumDescriptions": ["Unspecified finding class.", "Describes unwanted or malicious activity.", "Describes a potential weakness in software that increases risk to Confidentiality & Integrity & Availability.", "Describes a potential weakness in cloud resource/asset configuration that increases risk.", "Describes a security observation that is for informational purposes.", "Describes an error that prevents some SCC functionality.", "Describes a potential security risk due to a change in the security posture."], "type": "string"}, "iamBindings": {"description": "Represents IAM bindings associated with the finding.", "items": {"$ref": "<PERSON>am<PERSON><PERSON><PERSON>"}, "type": "array"}, "indicator": {"$ref": "Indicator", "description": "Represents what's commonly known as an *indicator of compromise* (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise)."}, "kernelRootkit": {"$ref": "KernelRootkit", "description": "Signature of the kernel rootkit."}, "kubernetes": {"$ref": "Kubernetes", "description": "Kubernetes resources associated with the finding."}, "loadBalancers": {"description": "The load balancers associated with the finding.", "items": {"$ref": "LoadBalancer"}, "type": "array"}, "logEntries": {"description": "Log entries that are relevant to the finding.", "items": {"$ref": "LogEntry"}, "type": "array"}, "mitreAttack": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org"}, "moduleName": {"description": "Unique identifier of the module which generated the finding. Example: folders/598186756061/securityHealthAnalyticsSettings/customModules/56799441161885", "type": "string"}, "mute": {"description": "Indicates the mute state of a finding (either muted, unmuted or undefined). Unlike other attributes of a finding, a finding provider shouldn't set the value of mute.", "enum": ["MUTE_UNSPECIFIED", "MUTED", "UNMUTED", "UNDEFINED"], "enumDescriptions": ["Unspecified.", "Finding has been muted.", "Finding has been unmuted.", "Finding has never been muted/unmuted."], "type": "string"}, "muteInitiator": {"description": "Records additional information about the mute operation, for example, the [mute configuration](/security-command-center/docs/how-to-mute-findings) that muted the finding and the user who muted the finding.", "type": "string"}, "muteUpdateTime": {"description": "Output only. The most recent time this finding was muted or unmuted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "The [relative resource name](https://cloud.google.com/apis/design/resource_names#relative_resource_name) of the finding. Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\", \"projects/{project_id}/sources/{source_id}/findings/{finding_id}\".", "type": "string"}, "nextSteps": {"description": "Steps to address the finding.", "type": "string"}, "orgPolicies": {"description": "Contains information about the org policies associated with the finding.", "items": {"$ref": "OrgPolicy"}, "type": "array"}, "parent": {"description": "The relative resource name of the source the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. For example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}, "parentDisplayName": {"description": "Output only. The human readable display name of the finding source such as \"Event Threat Detection\" or \"Security Health Analytics\".", "readOnly": true, "type": "string"}, "processes": {"description": "Represents operating system processes associated with the Finding.", "items": {"$ref": "Process"}, "type": "array"}, "resourceName": {"description": "For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string. This field is immutable after creation time.", "type": "string"}, "securityMarks": {"$ref": "SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "securityPosture": {"$ref": "SecurityPosture", "description": "The security posture associated with the finding."}, "severity": {"description": "The severity of the finding. This field is managed by the source that writes the finding.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["This value is used for findings when a source doesn't write a severity value.", "Vulnerability: A critical vulnerability is easily discoverable by an external actor, exploitable, and results in the direct ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. Examples include publicly accessible unprotected user data and public SSH access with weak or no passwords. Threat: Indicates a threat that is able to access, modify, or delete data or execute unauthorized code within existing resources.", "Vulnerability: A high risk vulnerability can be easily discovered and exploited in combination with other vulnerabilities in order to gain direct access and the ability to execute arbitrary code, exfiltrate data, and otherwise gain additional access and privileges to cloud resources and workloads. An example is a database with weak or no passwords that is only accessible internally. This database could easily be compromised by an actor that had access to the internal network. Threat: Indicates a threat that is able to create new computational resources in an environment but not able to access data or execute code in existing resources.", "Vulnerability: A medium risk vulnerability could be used by an actor to gain access to resources or privileges that enable them to eventually (through multiple steps or a complex exploit) gain access and the ability to execute arbitrary code or exfiltrate data. An example is a service account with access to more projects than it should have. If an actor gains access to the service account, they could potentially use that access to manipulate a project the service account was not intended to. Threat: Indicates a threat that is able to cause operational impact but may not access data or execute unauthorized code.", "Vulnerability: A low risk vulnerability hampers a security organization's ability to detect vulnerabilities or active threats in their deployment, or prevents the root cause investigation of security issues. An example is monitoring and logs being disabled for resource configurations and access. Threat: Indicates a threat that has obtained minimal access to an environment but is not able to access data, execute code, or create resources."], "type": "string"}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}, "vulnerability": {"$ref": "Vulnerability", "description": "Represents vulnerability-specific fields like CVE and CVSS scores. CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}}, "type": "object"}, "Folder": {"description": "Message that contains the resource name and display name of a folder resource.", "id": "Folder", "properties": {"resourceFolder": {"description": "Full resource name of this folder. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceFolderDisplayName": {"description": "The user defined display name for this folder.", "type": "string"}}, "type": "object"}, "Geolocation": {"description": "Represents a geographical location for a given access.", "id": "Geolocation", "properties": {"regionCode": {"description": "A CLDR.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1BigQueryExport": {"description": "Configures how to deliver Findings to BigQuery Instance.", "id": "GoogleCloudSecuritycenterV1BigQueryExport", "properties": {"createTime": {"description": "Output only. The time at which the BigQuery export was created. This field is set by the server and will be ignored if provided on export on creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "dataset": {"description": "The dataset to write findings' updates to. Its format is \"projects/[project_id]/datasets/[bigquery_dataset_id]\". BigQuery Dataset unique ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_).", "type": "string"}, "description": {"description": "The description of the export (max of 1024 characters).", "type": "string"}, "filter": {"description": "Expression that defines the filter to apply across create/update events of findings. The expression is a list of zero or more restrictions combined via logical operators `AND` and `OR`. Parentheses are supported, and `OR` has higher precedence than `AND`. Restrictions have the form ` ` and may have a `-` character in front of them to indicate negation. The fields map to those defined in the corresponding resource. The supported operators are: * `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching, for strings. The supported value types are: * string literals in quotes. * integer literals without quotes. * boolean literals `true` and `false` without quotes.", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the BigQuery export. This field is set by the server and will be ignored if provided on export creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "The relative resource name of this export. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name. Example format: \"organizations/{organization_id}/bigQueryExports/{export_id}\" Example format: \"folders/{folder_id}/bigQueryExports/{export_id}\" Example format: \"projects/{project_id}/bigQueryExports/{export_id}\" This field is provided in responses, and is ignored when provided in create requests.", "type": "string"}, "principal": {"description": "Output only. The service account that needs permission to create table and upload data to the BigQuery dataset.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the BigQuery export was updated. This field is set by the server and will be ignored if provided on export creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1Binding": {"description": "Represents a Kubernetes RoleBinding or ClusterRoleBinding.", "id": "GoogleCloudSecuritycenterV1Binding", "properties": {"name": {"description": "Name for the binding.", "type": "string"}, "ns": {"description": "Namespace for the binding.", "type": "string"}, "role": {"$ref": "Role", "description": "The Role or ClusterRole referenced by the binding."}, "subjects": {"description": "Represents one or more subjects that are bound to the role. Not always available for PATCH requests.", "items": {"$ref": "Subject"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1BulkMuteFindingsResponse": {"description": "The response to a BulkMute request. Contains the LRO information.", "id": "GoogleCloudSecuritycenterV1BulkMuteFindingsResponse", "properties": {}, "type": "object"}, "GoogleCloudSecuritycenterV1CustomConfig": {"description": "Defines the properties in a custom module configuration for Security Health Analytics. Use the custom module configuration to create custom detectors that generate custom findings for resources that you specify.", "id": "GoogleCloudSecuritycenterV1CustomConfig", "properties": {"customOutput": {"$ref": "GoogleCloudSecuritycenterV1CustomOutputSpec", "description": "Custom output properties."}, "description": {"description": "Text that describes the vulnerability or misconfiguration that the custom module detects. This explanation is returned with each finding instance to help investigators understand the detected issue. The text must be enclosed in quotation marks.", "type": "string"}, "predicate": {"$ref": "Expr", "description": "The CEL expression to evaluate to produce findings. When the expression evaluates to true against a resource, a finding is generated."}, "recommendation": {"description": "An explanation of the recommended steps that security teams can take to resolve the detected issue. This explanation is returned with each finding generated by this module in the `nextSteps` property of the finding JSON.", "type": "string"}, "resourceSelector": {"$ref": "GoogleCloudSecuritycenterV1ResourceSelector", "description": "The resource types that the custom module operates on. Each custom module can specify up to 5 resource types."}, "severity": {"description": "The severity to assign to findings generated by the module.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["Unspecified severity.", "Critical severity.", "High severity.", "Medium severity.", "Low severity."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1CustomOutputSpec": {"description": "A set of optional name-value pairs that define custom source properties to return with each finding that is generated by the custom module. The custom source properties that are defined here are included in the finding JSON under `sourceProperties`.", "id": "GoogleCloudSecuritycenterV1CustomOutputSpec", "properties": {"properties": {"description": "A list of custom output properties to add to the finding.", "items": {"$ref": "GoogleCloudSecuritycenterV1Property"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule": {"description": "An EffectiveSecurityHealthAnalyticsCustomModule is the representation of a Security Health Analytics custom module at a specified level of the resource hierarchy: organization, folder, or project. If a custom module is inherited from a parent organization or folder, the value of the `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule is set to the value that is effective in the parent, instead of `INHERITED`. For example, if the module is enabled in a parent organization or folder, the effective enablement_state for the module in all child folders or projects is also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is read-only.", "id": "GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule", "properties": {"customConfig": {"$ref": "GoogleCloudSecuritycenterV1CustomConfig", "description": "Output only. The user-specified configuration for the module.", "readOnly": true}, "displayName": {"description": "Output only. The display name for the custom module. The name must be between 1 and 128 characters, start with a lowercase letter, and contain alphanumeric characters or underscores only.", "readOnly": true, "type": "string"}, "enablementState": {"description": "Output only. The effective state of enablement for the module at the given level of the hierarchy.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["Unspecified enablement state.", "The module is enabled at the given level.", "The module is disabled at the given level."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. The resource name of the custom module. Its format is \"organizations/{organization}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\", or \"folders/{folder}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\", or \"projects/{project}/securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}\"", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ExternalSystem": {"description": "Representation of third party SIEM/SOAR fields within SCC.", "id": "GoogleCloudSecuritycenterV1ExternalSystem", "properties": {"assignees": {"description": "References primary/secondary etc assignees in the external system.", "items": {"type": "string"}, "type": "array"}, "externalSystemUpdateTime": {"description": "The time when the case was last updated, as reported by the external system.", "format": "google-datetime", "type": "string"}, "externalUid": {"description": "The identifier that's used to track the finding's corresponding case in the external system.", "type": "string"}, "name": {"description": "Full resource name of the external system, for example: \"organizations/1234/sources/5678/findings/123456/externalSystems/jira\", \"folders/1234/sources/5678/findings/123456/externalSystems/jira\", \"projects/1234/sources/5678/findings/123456/externalSystems/jira\"", "type": "string"}, "status": {"description": "The most recent status of the finding's corresponding case, as reported by the external system.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1MuteConfig": {"description": "A mute config is a Cloud SCC resource that contains the configuration to mute create/update events of findings.", "id": "GoogleCloudSecuritycenterV1MuteConfig", "properties": {"createTime": {"description": "Output only. The time at which the mute config was created. This field is set by the server and will be ignored if provided on config creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "A description of the mute config.", "type": "string"}, "displayName": {"deprecated": true, "description": "The human readable name to be displayed for the mute config.", "type": "string"}, "filter": {"description": "Required. An expression that defines the filter to apply across create/update events of findings. While creating a filter string, be mindful of the scope in which the mute configuration is being created. E.g., If a filter contains project = X but is created under the project = Y scope, it might not match any findings. The following field and operator combinations are supported: * severity: `=`, `:` * category: `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` * resource.project_display_name: `=`, `:` * resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`, `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` * finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` * indicator.domains: `=`, `:`", "type": "string"}, "mostRecentEditor": {"description": "Output only. Email address of the user who last edited the mute config. This field is set by the server and will be ignored if provided on config creation or update.", "readOnly": true, "type": "string"}, "name": {"description": "This field will be ignored if provided on config creation. Format \"organizations/{organization}/muteConfigs/{mute_config}\" \"folders/{folder}/muteConfigs/{mute_config}\" \"projects/{project}/muteConfigs/{mute_config}\" \"organizations/{organization}/locations/global/muteConfigs/{mute_config}\" \"folders/{folder}/locations/global/muteConfigs/{mute_config}\" \"projects/{project}/locations/global/muteConfigs/{mute_config}\"", "type": "string"}, "updateTime": {"description": "Output only. The most recent time at which the mute config was updated. This field is set by the server and will be ignored if provided on config creation or update.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1NotificationMessage": {"description": "Cloud SCC's Notification", "id": "GoogleCloudSecuritycenterV1NotificationMessage", "properties": {"finding": {"$ref": "Finding", "description": "If it's a Finding based notification config, this field will be populated."}, "notificationConfigName": {"description": "Name of the notification config that generated current notification.", "type": "string"}, "resource": {"$ref": "GoogleCloudSecuritycenterV1Resource", "description": "The Cloud resource tied to this notification's Finding."}}, "type": "object"}, "GoogleCloudSecuritycenterV1Property": {"description": "An individual name-value pair that defines a custom source property.", "id": "GoogleCloudSecuritycenterV1Property", "properties": {"name": {"description": "Name of the property for the custom output.", "type": "string"}, "valueExpression": {"$ref": "Expr", "description": "The CEL expression for the custom output. A resource property can be specified to return the value of the property or a text string enclosed in quotation marks."}}, "type": "object"}, "GoogleCloudSecuritycenterV1Resource": {"description": "Information related to the Google Cloud resource.", "id": "GoogleCloudSecuritycenterV1Resource", "properties": {"displayName": {"description": "The human readable name of the resource.", "type": "string"}, "folders": {"description": "Output only. Contains a Folder message for each folder in the assets ancestry. The first folder is the deepest nested folder, and the last folder is the folder directly under the Organization.", "items": {"$ref": "Folder"}, "readOnly": true, "type": "array"}, "name": {"description": "The full resource name of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "parent": {"description": "The full resource name of resource's parent.", "type": "string"}, "parentDisplayName": {"description": "The human readable name of resource's parent.", "type": "string"}, "project": {"description": "The full resource name of project that the resource belongs to.", "type": "string"}, "projectDisplayName": {"description": "The project ID that the resource belongs to.", "type": "string"}, "type": {"description": "The full resource type of the resource.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ResourceSelector": {"description": "Resource for selecting resource type.", "id": "GoogleCloudSecuritycenterV1ResourceSelector", "properties": {"resourceTypes": {"description": "The resource types to run the detector on.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudSecuritycenterV1ResourceValueConfig": {"description": "A resource value config (RVC) is a mapping configuration of user's resources to resource values. Used in Attack path simulations.", "id": "GoogleCloudSecuritycenterV1ResourceValueConfig", "properties": {"createTime": {"description": "Output only. Timestamp this resource value config was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Description of the resource value config.", "type": "string"}, "name": {"description": "Name for the resource value config", "type": "string"}, "resourceLabelsSelector": {"additionalProperties": {"type": "string"}, "description": "List of resource labels to search for, evaluated with AND. E.g. \"resource_labels_selector\": {\"key\": \"value\", \"env\": \"prod\"} will match resources with labels \"key\": \"value\" AND \"env\": \"prod\" https://cloud.google.com/resource-manager/docs/creating-managing-labels", "type": "object"}, "resourceType": {"description": "Apply resource_value only to resources that match resource_type. resource_type will be checked with \"AND\" of other resources. E.g. \"storage.googleapis.com/Bucket\" with resource_value \"HIGH\" will apply \"HIGH\" value only to \"storage.googleapis.com/Bucket\" resources.", "type": "string"}, "resourceValue": {"description": "Required. Resource value level this expression represents", "enum": ["RESOURCE_VALUE_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "NONE"], "enumDescriptions": ["Unspecific value", "High resource value", "Medium resource value", "Low resource value", "No resource value, e.g. ignore these resources"], "type": "string"}, "scope": {"description": "Project or folder to scope this config to. For example, \"project/456\" would apply this config only to resources in \"project/456\" scope will be checked with \"AND\" of other resources.", "type": "string"}, "tagValues": {"description": "Required. Tag values combined with AND to check against. Values in the form \"tagValues/123\" E.g. [ \"tagValues/123\", \"tagValues/456\", \"tagValues/789\" ] https://cloud.google.com/resource-manager/docs/tags/tags-creating-and-managing", "items": {"type": "string"}, "type": "array"}, "updateTime": {"description": "Output only. Timestamp this resource value config was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule": {"description": "Represents an instance of a Security Health Analytics custom module, including its full module name, display name, enablement state, and last updated time. You can create a custom module at the organization, folder, or project level. Custom modules that you create at the organization or folder level are inherited by the child folders and projects.", "id": "GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule", "properties": {"ancestorModule": {"description": "Output only. If empty, indicates that the custom module was created in the organization, folder, or project in which you are viewing the custom module. Otherwise, `ancestor_module` specifies the organization or folder from which the custom module is inherited.", "readOnly": true, "type": "string"}, "customConfig": {"$ref": "GoogleCloudSecuritycenterV1CustomConfig", "description": "The user specified custom configuration for the module."}, "displayName": {"description": "The display name of the Security Health Analytics custom module. This display name becomes the finding category for all findings that are returned by this custom module. The display name must be between 1 and 128 characters, start with a lowercase letter, and contain alphanumeric characters or underscores only.", "type": "string"}, "enablementState": {"description": "The enablement state of the custom module.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "ENABLED", "DISABLED", "INHERITED"], "enumDescriptions": ["Unspecified enablement state.", "The module is enabled at the given CRM resource.", "The module is disabled at the given CRM resource.", "State is inherited from an ancestor module. The module will either be effectively ENABLED or DISABLED based on its closest non-inherited ancestor module in the CRM hierarchy."], "type": "string"}, "lastEditor": {"description": "Output only. The editor that last updated the custom module.", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The resource name of the custom module. Its format is \"organizations/{organization}/securityHealthAnalyticsSettings/customModules/{customModule}\", or \"folders/{folder}/securityHealthAnalyticsSettings/customModules/{customModule}\", or \"projects/{project}/securityHealthAnalyticsSettings/customModules/{customModule}\" The id {customModule} is server-generated and is not user settable. It will be a numeric id containing 1-20 digits.", "type": "string"}, "updateTime": {"description": "Output only. The time at which the custom module was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Finding": {"description": "Security Command Center finding. A finding is a record of assessment data (security, risk, health or privacy) ingested into Security Command Center for presentation, notification, analysis, policy testing, and enforcement. For example, an XSS vulnerability in an App Engine application is a finding.", "id": "GoogleCloudSecuritycenterV1p1beta1Finding", "properties": {"canonicalName": {"description": "The canonical name of the finding. It's either \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\", \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}\" or \"projects/{project_number}/sources/{source_id}/findings/{finding_id}\", depending on the closest CRM ancestor of the resource associated with the finding.", "type": "string"}, "category": {"description": "The additional taxonomy group within findings from a given source. This field is immutable after creation time. Example: \"XSS_FLASH_INJECTION\"", "type": "string"}, "createTime": {"description": "The time at which the finding was created in Security Command Center.", "format": "google-datetime", "type": "string"}, "eventTime": {"description": "The time at which the event took place, or when an update to the finding occurred. For example, if the finding represents an open firewall it would capture the time the detector believes the firewall became open. The accuracy is determined by the detector. If the finding were to be resolved afterward, this time would reflect when the finding was resolved. Must not be set to a value greater than the current timestamp.", "format": "google-datetime", "type": "string"}, "externalUri": {"description": "The URI that, if available, points to a web page outside of Security Command Center where additional information about the finding can be found. This field is guaranteed to be either empty or a well formed URL.", "type": "string"}, "name": {"description": "The relative resource name of this finding. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Example: \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\"", "type": "string"}, "parent": {"description": "The relative resource name of the source the finding belongs to. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name This field is immutable after creation time. For example: \"organizations/{organization_id}/sources/{source_id}\"", "type": "string"}, "resourceName": {"description": "For findings on Google Cloud resources, the full resource name of the Google Cloud resource this finding is for. See: https://cloud.google.com/apis/design/resource_names#full_resource_name When the finding is for a non-Google Cloud resource, the resourceName can be a customer or partner defined string. This field is immutable after creation time.", "type": "string"}, "securityMarks": {"$ref": "GoogleCloudSecuritycenterV1p1beta1SecurityMarks", "description": "Output only. User specified security marks. These marks are entirely managed by the user and come from the SecurityMarks resource that belongs to the finding.", "readOnly": true}, "severity": {"description": "The severity of the finding. This field is managed by the source that writes the finding.", "enum": ["SEVERITY_UNSPECIFIED", "CRITICAL", "HIGH", "MEDIUM", "LOW"], "enumDescriptions": ["No severity specified. The default value.", "Critical severity.", "High severity.", "Medium severity.", "Low severity."], "type": "string"}, "sourceProperties": {"additionalProperties": {"type": "any"}, "description": "Source specific properties. These properties are managed by the source that writes the finding. The key names in the source_properties map must be between 1 and 255 characters, and must start with a letter and contain alphanumeric characters or underscores only.", "type": "object"}, "state": {"description": "The state of the finding.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE"], "enumDescriptions": ["Unspecified state.", "The finding requires attention and has not been addressed yet.", "The finding has been fixed, triaged as a non-issue or otherwise addressed and is no longer active."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Folder": {"description": "Message that contains the resource name and display name of a folder resource.", "id": "GoogleCloudSecuritycenterV1p1beta1Folder", "properties": {"resourceFolder": {"description": "Full resource name of this folder. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "resourceFolderDisplayName": {"description": "The user defined display name for this folder.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1NotificationMessage": {"description": "Security Command Center's Notification", "id": "GoogleCloudSecuritycenterV1p1beta1NotificationMessage", "properties": {"finding": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Finding", "description": "If it's a Finding based notification config, this field will be populated."}, "notificationConfigName": {"description": "Name of the notification config that generated current notification.", "type": "string"}, "resource": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Resource", "description": "The Cloud resource tied to the notification."}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1Resource": {"description": "Information related to the Google Cloud resource.", "id": "GoogleCloudSecuritycenterV1p1beta1Resource", "properties": {"folders": {"description": "Output only. Contains a Folder message for each folder in the assets ancestry. The first folder is the deepest nested folder, and the last folder is the folder directly under the Organization.", "items": {"$ref": "GoogleCloudSecuritycenterV1p1beta1Folder"}, "readOnly": true, "type": "array"}, "name": {"description": "The full resource name of the resource. See: https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}, "parent": {"description": "The full resource name of resource's parent.", "type": "string"}, "parentDisplayName": {"description": "The human readable name of resource's parent.", "type": "string"}, "project": {"description": "The full resource name of project that the resource belongs to.", "type": "string"}, "projectDisplayName": {"description": "The project id that the resource belongs to.", "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse": {"description": "Response of asset discovery run", "id": "GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse", "properties": {"duration": {"description": "The duration between asset discovery run start and end", "format": "google-duration", "type": "string"}, "state": {"description": "The state of an asset discovery run.", "enum": ["STATE_UNSPECIFIED", "COMPLETED", "SUPERSEDED", "TERMINATED"], "enumDescriptions": ["Asset discovery run state was unspecified.", "Asset discovery run completed successfully.", "Asset discovery run was cancelled with tasks still pending, as another run for the same organization was started with a higher priority.", "Asset discovery run was killed and terminated."], "type": "string"}}, "type": "object"}, "GoogleCloudSecuritycenterV1p1beta1SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "GoogleCloudSecuritycenterV1p1beta1SecurityMarks", "properties": {"canonicalName": {"description": "The canonical name of the marks. Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"folders/{folder_id}/assets/{asset_id}/securityMarks\" \"projects/{project_number}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"projects/{project_number}/sources/{source_id}/findings/{finding_id}/securityMarks\"", "type": "string"}, "marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "type": "string"}}, "type": "object"}, "IamBinding": {"description": "Represents a particular IAM binding, which captures a member's role addition, removal, or state.", "id": "<PERSON>am<PERSON><PERSON><PERSON>", "properties": {"action": {"description": "The action that was performed on a Binding.", "enum": ["ACTION_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["Unspecified.", "Addition of a Binding.", "Removal of a Binding."], "type": "string"}, "member": {"description": "A single identity requesting access for a Cloud Platform resource, for example, \"<EMAIL>\".", "type": "string"}, "role": {"description": "Role that is assigned to \"members\". For example, \"roles/viewer\", \"roles/editor\", or \"roles/owner\".", "type": "string"}}, "type": "object"}, "Indicator": {"description": "Represents what's commonly known as an _indicator of compromise_ (IoC) in computer forensics. This is an artifact observed on a network or in an operating system that, with high confidence, indicates a computer intrusion. For more information, see [Indicator of compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).", "id": "Indicator", "properties": {"domains": {"description": "List of domains associated to the Finding.", "items": {"type": "string"}, "type": "array"}, "ipAddresses": {"description": "The list of IP addresses that are associated with the finding.", "items": {"type": "string"}, "type": "array"}, "signatures": {"description": "The list of matched signatures indicating that the given process is present in the environment.", "items": {"$ref": "ProcessSignature"}, "type": "array"}, "uris": {"description": "The list of URIs associated to the Findings.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "KernelRootkit": {"description": "Kernel mode rootkit signatures.", "id": "KernelRootkit", "properties": {"name": {"description": "Rootkit name, when available.", "type": "string"}, "unexpectedCodeModification": {"description": "True if unexpected modifications of kernel code memory are present.", "type": "boolean"}, "unexpectedFtraceHandler": {"description": "True if `ftrace` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedInterruptHandler": {"description": "True if interrupt handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKernelCodePages": {"description": "True if kernel code pages that are not in the expected kernel or module code regions are present.", "type": "boolean"}, "unexpectedKprobeHandler": {"description": "True if `kprobe` points are present with callbacks pointing to regions that are not in the expected kernel or module code range.", "type": "boolean"}, "unexpectedProcessesInRunqueue": {"description": "True if unexpected processes in the scheduler run queue are present. Such processes are in the run queue, but not in the process task list.", "type": "boolean"}, "unexpectedReadOnlyDataModification": {"description": "True if unexpected modifications of kernel read-only data memory are present.", "type": "boolean"}, "unexpectedSystemCallHandler": {"description": "True if system call handlers that are are not in the expected kernel or module code regions are present.", "type": "boolean"}}, "type": "object"}, "Kubernetes": {"description": "Kubernetes-related attributes.", "id": "Kubernetes", "properties": {"accessReviews": {"description": "Provides information on any Kubernetes access reviews (privilege checks) relevant to the finding.", "items": {"$ref": "AccessReview"}, "type": "array"}, "bindings": {"description": "Provides Kubernetes role binding information for findings that involve [RoleBindings or ClusterRoleBindings](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "GoogleCloudSecuritycenterV1Binding"}, "type": "array"}, "nodePools": {"description": "GKE [node pools](https://cloud.google.com/kubernetes-engine/docs/concepts/node-pools) associated with the finding. This field contains node pool information for each node, when it is available.", "items": {"$ref": "NodePool"}, "type": "array"}, "nodes": {"description": "Provides Kubernetes [node](https://cloud.google.com/kubernetes-engine/docs/concepts/cluster-architecture#nodes) information.", "items": {"$ref": "Node"}, "type": "array"}, "objects": {"description": "Kubernetes objects related to the finding.", "items": {"$ref": "Object"}, "type": "array"}, "pods": {"description": "Kubernetes [Pods](https://cloud.google.com/kubernetes-engine/docs/concepts/pod) associated with the finding. This field contains Pod records for each container that is owned by a Pod.", "items": {"$ref": "Pod"}, "type": "array"}, "roles": {"description": "Provides Kubernetes role information for findings that involve [Roles or ClusterRoles](https://cloud.google.com/kubernetes-engine/docs/how-to/role-based-access-control).", "items": {"$ref": "Role"}, "type": "array"}}, "type": "object"}, "Label": {"description": "Represents a generic name-value label. A label has separate name and value fields to support filtering with the `contains()` function. For more information, see [Filtering on array-type fields](https://cloud.google.com/security-command-center/docs/how-to-api-list-findings#array-contains-filtering).", "id": "Label", "properties": {"name": {"description": "Name of the label.", "type": "string"}, "value": {"description": "Value that corresponds to the label's name.", "type": "string"}}, "type": "object"}, "LoadBalancer": {"description": "Contains information related to the load balancer associated with the finding.", "id": "LoadBalancer", "properties": {"name": {"description": "The name of the load balancer associated with the finding.", "type": "string"}}, "type": "object"}, "LogEntry": {"description": "An individual entry in a log.", "id": "LogEntry", "properties": {"cloudLoggingEntry": {"$ref": "CloudLoggingEntry", "description": "An individual entry in a log stored in Cloud Logging."}}, "type": "object"}, "MemoryHashSignature": {"description": "A signature corresponding to memory page hashes.", "id": "MemoryHashSignature", "properties": {"binaryFamily": {"description": "The binary family.", "type": "string"}, "detections": {"description": "The list of memory hash detections contributing to the binary family match.", "items": {"$ref": "Detection"}, "type": "array"}}, "type": "object"}, "MitreAttack": {"description": "MITRE ATT&CK tactics and techniques related to this finding. See: https://attack.mitre.org", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"additionalTactics": {"description": "Additional MITRE ATT&CK tactics related to this finding, if any.", "items": {"enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "type": "array"}, "additionalTechniques": {"description": "Additional MITRE ATT&CK techniques related to this finding, if any, along with any of their respective parent techniques.", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "SHARED_MODULES", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "RESOURCE_HIJACKING", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "DYNAMIC_RESOLUTION", "UNSECURED_CREDENTIALS", "VALID_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "NETWORK_DENIAL_OF_SERVICE", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "ACCOUNT_MANIPULATION", "SSH_AUTHORIZED_KEYS", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "STEAL_WEB_SESSION_COOKIE", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "EXPLOIT_PUBLIC_FACING_APPLICATION", "MODIFY_AUTHENTICATION_PROCESS", "DATA_DESTRUCTION", "DOMAIN_POLICY_MODIFICATION", "IMPAIR_DEFENSES", "NETWORK_SERVICE_DISCOVERY", "ACCESS_TOKEN_MANIPULATION", "ABUSE_ELEVATION_CONTROL_MECHANISM", "DEFAULT_ACCOUNTS", "INHIBIT_SYSTEM_RECOVERY"], "enumDescriptions": ["Unspecified value.", "T1595", "T1595.001", "T1105", "T1106", "T1129", "T1059", "T1059.004", "T1496", "T1090", "T1090.002", "T1090.003", "T1568", "T1552", "T1078", "T1078.003", "T1078.004", "T1498", "T1069", "T1069.003", "T1567", "T1567.002", "T1098", "T1098.004", "T1543", "T1539", "T1578", "T1190", "T1556", "T1485", "T1484", "T1562", "T1046", "T1134", "T1548", "T1078.001", "T1490"], "type": "string"}, "type": "array"}, "primaryTactic": {"description": "The MITRE ATT&CK tactic most closely represented by this finding, if any.", "enum": ["TACTIC_UNSPECIFIED", "RECONNAISSANCE", "RESOURCE_DEVELOPMENT", "INITIAL_ACCESS", "EXECUTION", "PERSISTENCE", "PRIVILEGE_ESCALATION", "DEFENSE_EVASION", "CREDENTIAL_ACCESS", "DISCOVERY", "LATERAL_MOVEMENT", "COLLECTION", "COMMAND_AND_CONTROL", "EXFILTRATION", "IMPACT"], "enumDescriptions": ["Unspecified value.", "TA0043", "TA0042", "TA0001", "TA0002", "TA0003", "TA0004", "TA0005", "TA0006", "TA0007", "TA0008", "TA0009", "TA0011", "TA0010", "TA0040"], "type": "string"}, "primaryTechniques": {"description": "The MITRE ATT&CK technique most closely represented by this finding, if any. primary_techniques is a repeated field because there are multiple levels of MITRE ATT&CK techniques. If the technique most closely represented by this finding is a sub-technique (e.g. `SCANNING_IP_BLOCKS`), both the sub-technique and its parent technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`, `ACTIVE_SCANNING`).", "items": {"enum": ["TECHNIQUE_UNSPECIFIED", "ACTIVE_SCANNING", "SCANNING_IP_BLOCKS", "INGRESS_TOOL_TRANSFER", "NATIVE_API", "SHARED_MODULES", "COMMAND_AND_SCRIPTING_INTERPRETER", "UNIX_SHELL", "RESOURCE_HIJACKING", "PROXY", "EXTERNAL_PROXY", "MULTI_HOP_PROXY", "DYNAMIC_RESOLUTION", "UNSECURED_CREDENTIALS", "VALID_ACCOUNTS", "LOCAL_ACCOUNTS", "CLOUD_ACCOUNTS", "NETWORK_DENIAL_OF_SERVICE", "PERMISSION_GROUPS_DISCOVERY", "CLOUD_GROUPS", "EXFILTRATION_OVER_WEB_SERVICE", "EXFILTRATION_TO_CLOUD_STORAGE", "ACCOUNT_MANIPULATION", "SSH_AUTHORIZED_KEYS", "CREATE_OR_MODIFY_SYSTEM_PROCESS", "STEAL_WEB_SESSION_COOKIE", "MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE", "EXPLOIT_PUBLIC_FACING_APPLICATION", "MODIFY_AUTHENTICATION_PROCESS", "DATA_DESTRUCTION", "DOMAIN_POLICY_MODIFICATION", "IMPAIR_DEFENSES", "NETWORK_SERVICE_DISCOVERY", "ACCESS_TOKEN_MANIPULATION", "ABUSE_ELEVATION_CONTROL_MECHANISM", "DEFAULT_ACCOUNTS", "INHIBIT_SYSTEM_RECOVERY"], "enumDescriptions": ["Unspecified value.", "T1595", "T1595.001", "T1105", "T1106", "T1129", "T1059", "T1059.004", "T1496", "T1090", "T1090.002", "T1090.003", "T1568", "T1552", "T1078", "T1078.003", "T1078.004", "T1498", "T1069", "T1069.003", "T1567", "T1567.002", "T1098", "T1098.004", "T1543", "T1539", "T1578", "T1190", "T1556", "T1485", "T1484", "T1562", "T1046", "T1134", "T1548", "T1078.001", "T1490"], "type": "string"}, "type": "array"}, "version": {"description": "The MITRE ATT&CK version referenced by the above fields. E.g. \"8\".", "type": "string"}}, "type": "object"}, "Node": {"description": "Kubernetes nodes associated with the finding.", "id": "Node", "properties": {"name": {"description": "[Full resource name](https://google.aip.dev/122#full-resource-names) of the Compute Engine VM running the cluster node.", "type": "string"}}, "type": "object"}, "NodePool": {"description": "Provides GKE node pool information.", "id": "NodePool", "properties": {"name": {"description": "Kubernetes node pool name.", "type": "string"}, "nodes": {"description": "Nodes associated with the finding.", "items": {"$ref": "Node"}, "type": "array"}}, "type": "object"}, "Object": {"description": "Kubernetes object related to the finding, uniquely identified by GKNN. Used if the object Kind is not one of Pod, Node, NodePool, Binding, or AccessReview.", "id": "Object", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "Container"}, "type": "array"}, "group": {"description": "Kubernetes object group, such as \"policy.k8s.io/v1\".", "type": "string"}, "kind": {"description": "Kubernetes object kind, such as “Namespace”.", "type": "string"}, "name": {"description": "Kubernetes object name. For details see https://kubernetes.io/docs/concepts/overview/working-with-objects/names/.", "type": "string"}, "ns": {"description": "Kubernetes object namespace. Must be a valid DNS label. Named \"ns\" to avoid collision with C++ namespace keyword. For details see https://kubernetes.io/docs/tasks/administer-cluster/namespaces/.", "type": "string"}}, "type": "object"}, "OrgPolicy": {"description": "Contains information about the org policies associated with the finding.", "id": "OrgPolicy", "properties": {"name": {"description": "The resource name of the org policy. Example: \"organizations/{organization_id}/policies/{constraint_name}\"", "type": "string"}}, "type": "object"}, "Pod": {"description": "A Kubernetes Pod.", "id": "Pod", "properties": {"containers": {"description": "Pod containers associated with this finding, if any.", "items": {"$ref": "Container"}, "type": "array"}, "labels": {"description": "Pod labels. For Kubernetes containers, these are applied to the container.", "items": {"$ref": "Label"}, "type": "array"}, "name": {"description": "Kubernetes Pod name.", "type": "string"}, "ns": {"description": "Kubernetes Pod namespace.", "type": "string"}}, "type": "object"}, "Process": {"description": "Represents an operating system process.", "id": "Process", "properties": {"args": {"description": "Process arguments as JSON encoded strings.", "items": {"type": "string"}, "type": "array"}, "argumentsTruncated": {"description": "True if `args` is incomplete.", "type": "boolean"}, "binary": {"$ref": "File", "description": "File information for the process executable."}, "envVariables": {"description": "Process environment variables.", "items": {"$ref": "EnvironmentVariable"}, "type": "array"}, "envVariablesTruncated": {"description": "True if `env_variables` is incomplete.", "type": "boolean"}, "libraries": {"description": "File information for libraries loaded by the process.", "items": {"$ref": "File"}, "type": "array"}, "name": {"description": "The process name, as displayed in utilities like `top` and `ps`. This name can be accessed through `/proc/[pid]/comm` and changed with `prctl(PR_SET_NAME)`.", "type": "string"}, "parentPid": {"description": "The parent process ID.", "format": "int64", "type": "string"}, "pid": {"description": "The process ID.", "format": "int64", "type": "string"}, "script": {"$ref": "File", "description": "When the process represents the invocation of a script, `binary` provides information about the interpreter, while `script` provides information about the script file provided to the interpreter."}}, "type": "object"}, "ProcessSignature": {"description": "Indicates what signature matched this process.", "id": "ProcessSignature", "properties": {"memoryHashSignature": {"$ref": "MemoryHashSignature", "description": "Signature indicating that a binary family was matched."}, "yaraRuleSignature": {"$ref": "YaraRuleSignature", "description": "Signature indicating that a YARA rule was matched."}}, "type": "object"}, "RapidVulnerabilityDetectionSettings": {"description": "Resource capturing the settings for the Rapid Vulnerability Detection service.", "id": "RapidVulnerabilityDetectionSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the RapidVulnerabilityDetectionSettings. Formats: * organizations/{organization}/rapidVulnerabilityDetectionSettings * folders/{folder}/rapidVulnerabilityDetectionSettings * projects/{project}/rapidVulnerabilityDetectionSettings", "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Reference": {"description": "Additional Links", "id": "Reference", "properties": {"source": {"description": "Source of the reference e.g. NVD", "type": "string"}, "uri": {"description": "Uri for the mentioned source e.g. https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527.", "type": "string"}}, "type": "object"}, "Role": {"description": "Kubernetes Role or ClusterRole.", "id": "Role", "properties": {"kind": {"description": "Role type.", "enum": ["KIND_UNSPECIFIED", "ROLE", "CLUSTER_ROLE"], "enumDescriptions": ["Role type is not specified.", "Kubernetes Role.", "Kubernetes ClusterRole."], "type": "string"}, "name": {"description": "Role name.", "type": "string"}, "ns": {"description": "Role namespace.", "type": "string"}}, "type": "object"}, "SecurityCenterSettings": {"description": "Resource capturing the settings for Security Center. Next ID: 12", "id": "SecurityCenterSettings", "properties": {"logSinkProject": {"description": "The resource name of the project to send logs to. This project must be part of the organization this resource resides in. The format is `projects/{project_id}`. An empty value disables logging. This value is only referenced by services that support log sink. Please refer to the documentation for an updated list of compatible services. This may only be specified for organization level onboarding.", "type": "string"}, "name": {"description": "The resource name of the SecurityCenterSettings. Format: organizations/{organization}/securityCenterSettings Format: folders/{folder}/securityCenterSettings Format: projects/{project}/securityCenterSettings", "type": "string"}, "onboardingTime": {"description": "Output only. Timestamp of when the customer organization was onboarded to SCC.", "format": "google-datetime", "readOnly": true, "type": "string"}, "orgServiceAccount": {"description": "Output only. The organization level service account to be used for security center components.", "readOnly": true, "type": "string"}}, "type": "object"}, "SecurityHealthAnalyticsSettings": {"description": "Resource capturing the settings for the Security Health Analytics service.", "id": "SecurityHealthAnalyticsSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the SecurityHealthAnalyticsSettings. Formats: * organizations/{organization}/securityHealthAnalyticsSettings * folders/{folder}/securityHealthAnalyticsSettings * projects/{project}/securityHealthAnalyticsSettings", "type": "string"}, "serviceAccount": {"description": "Output only. The service account used by Security Health Analytics detectors.", "readOnly": true, "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "SecurityMarks": {"description": "User specified security marks that are attached to the parent Security Command Center resource. Security marks are scoped within a Security Command Center organization -- they can be modified and viewed by all users who have proper permissions on the organization.", "id": "SecurityMarks", "properties": {"canonicalName": {"description": "The canonical name of the marks. Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"folders/{folder_id}/assets/{asset_id}/securityMarks\" \"projects/{project_number}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"folders/{folder_id}/sources/{source_id}/findings/{finding_id}/securityMarks\" \"projects/{project_number}/sources/{source_id}/findings/{finding_id}/securityMarks\"", "type": "string"}, "marks": {"additionalProperties": {"type": "string"}, "description": "Mutable user specified security marks belonging to the parent resource. Constraints are as follows: * Keys and values are treated as case insensitive * Keys must be between 1 - 256 characters (inclusive) * Keys must be letters, numbers, underscores, or dashes * Values have leading and trailing whitespace trimmed, remaining characters must be between 1 - 4096 characters (inclusive)", "type": "object"}, "name": {"description": "The relative resource name of the SecurityMarks. See: https://cloud.google.com/apis/design/resource_names#relative_resource_name Examples: \"organizations/{organization_id}/assets/{asset_id}/securityMarks\" \"organizations/{organization_id}/sources/{source_id}/findings/{finding_id}/securityMarks\".", "type": "string"}}, "type": "object"}, "SecurityPosture": {"description": "Represents a posture that is deployed on Google Cloud by the Security Command Center Posture Management service. A posture contains one or more policy sets. A policy set is a group of policies that enforce a set of security rules on Google Cloud.", "id": "SecurityPosture", "properties": {"changedPolicy": {"description": "The name of the policy that has been updated, for example, `projects/{project_id}/policies/{constraint_name}`.", "type": "string"}, "name": {"description": "Name of the posture, for example, `organizations/{org_id}/locations/{location}/postures/{posture_name}`.", "type": "string"}, "postureDeployment": {"description": "The name of the posture deployment, for example, `projects/{project_id}/posturedeployments/{posture_deployment_id}`.", "type": "string"}, "postureDeploymentResource": {"description": "The project, folder, or organization on which the posture is deployed, for example, `projects/{project_id}`.", "type": "string"}, "revisionId": {"description": "The version of the posture, for example, `c7cfa2a8`.", "type": "string"}}, "type": "object"}, "ServiceAccountDelegationInfo": {"description": "Identity delegation history of an authenticated service account.", "id": "ServiceAccountDelegationInfo", "properties": {"principalEmail": {"description": "The email address of a Google account.", "type": "string"}, "principalSubject": {"description": "A string representing the principal_subject associated with the identity. As compared to `principal_email`, supports principals that aren't associated with email addresses, such as third party principals. For most identities, the format will be `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}` except for some GKE identities (GKE_WORKLOAD, FREEFORM, GKE_HUB_WORKLOAD) that are still in the legacy format `serviceAccount:{identity pool name}[{subject}]`", "type": "string"}}, "type": "object"}, "Subject": {"description": "Represents a Kubernetes subject.", "id": "Subject", "properties": {"kind": {"description": "Authentication type for the subject.", "enum": ["AUTH_TYPE_UNSPECIFIED", "USER", "SERVICEACCOUNT", "GROUP"], "enumDescriptions": ["Authentication is not specified.", "User with valid certificate.", "Users managed by Kubernetes API with credentials stored as secrets.", "Collection of users."], "type": "string"}, "name": {"description": "Name for the subject.", "type": "string"}, "ns": {"description": "Namespace for the subject.", "type": "string"}}, "type": "object"}, "Subscription": {"description": "Resource capturing the state of an organization's subscription.", "id": "Subscription", "properties": {"details": {"$ref": "Details", "description": "The details of the most recent active subscription. If there has never been a subscription this will be empty."}, "name": {"description": "The resource name of the subscription. Format: organizations/{organization}/subscription", "type": "string"}, "tier": {"description": "The tier of SCC features this organization currently has access to.", "enum": ["TIER_UNSPECIFIED", "STANDARD", "PREMIUM", "ENTERPRISE"], "enumDescriptions": ["Default value. This value is unused.", "The standard tier.", "The premium tier.", "The enterprise tier."], "type": "string"}}, "type": "object"}, "VirtualMachineThreatDetectionSettings": {"description": "Resource capturing the settings for the Virtual Machine Threat Detection service.", "id": "VirtualMachineThreatDetectionSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the VirtualMachineThreatDetectionSettings. Formats: * organizations/{organization}/virtualMachineThreatDetectionSettings * folders/{folder}/virtualMachineThreatDetectionSettings * projects/{project}/virtualMachineThreatDetectionSettings", "type": "string"}, "serviceAccount": {"description": "Output only. The service account used by Virtual Machine Threat Detection detectors.", "readOnly": true, "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Vulnerability": {"description": "Refers to common vulnerability fields e.g. cve, cvss, cwe etc.", "id": "Vulnerability", "properties": {"cve": {"$ref": "Cve", "description": "CVE stands for Common Vulnerabilities and Exposures (https://cve.mitre.org/about/)"}}, "type": "object"}, "WebSecurityScannerSettings": {"description": "Resource capturing the settings for the Web Security Scanner service.", "id": "WebSecurityScannerSettings", "properties": {"modules": {"additionalProperties": {"$ref": "Config"}, "description": "The configurations including the state of enablement for the service's different modules. The absence of a module in the map implies its configuration is inherited from its parent's.", "type": "object"}, "name": {"description": "The resource name of the WebSecurityScannerSettings. Formats: * organizations/{organization}/webSecurityScannerSettings * folders/{folder}/webSecurityScannerSettings * projects/{project}/webSecurityScannerSettings", "type": "string"}, "serviceEnablementState": {"description": "The state of enablement for the service at its level of the resource hierarchy. A DISABLED state will override all module enablement_states to DISABLED.", "enum": ["ENABLEMENT_STATE_UNSPECIFIED", "INHERITED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default value. This value is unused.", "State is inherited from the parent resource.", "State is enabled.", "State is disabled."], "type": "string"}, "updateTime": {"description": "Output only. The time the settings were last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "YaraRuleSignature": {"description": "A signature corresponding to a YARA rule.", "id": "YaraRuleSignature", "properties": {"yaraRule": {"description": "The name of the YARA rule.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Security Command Center API", "version": "v1beta2", "version_module": true}