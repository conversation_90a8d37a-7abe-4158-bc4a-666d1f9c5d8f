import os
import sys
# DON'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from src.models.user import db
from src.routes.user import user_bp

# Try to serve built React app first, fallback to development static folder
static_folder = os.path.join(os.path.dirname(__file__), 'static', 'dist')
if not os.path.exists(static_folder):
    static_folder = os.path.join(os.path.dirname(__file__), 'static')

app = Flask(__name__, static_folder=static_folder)
app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'

app.register_blueprint(user_bp, url_prefix='/api')

# uncomment if you need to use database
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

@app.route('/health')
def health_check():
    """Health check endpoint for deployment platforms"""
    return {'status': 'healthy', 'message': 'Dice Rolling Game is running!', 'version': '1.2'}, 200

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


# For deployment compatibility
import os
port = int(os.environ.get('PORT', 5001))
debug = os.environ.get('FLASK_ENV') != 'production'

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=port, debug=debug)
else:
    # For WSGI servers (like Gunicorn)
    application = app
