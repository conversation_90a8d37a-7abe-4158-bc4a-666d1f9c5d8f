/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.hidden {
    display: none !important;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 400px;
    width: 90%;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-content h2 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.modal-content p {
    margin-bottom: 1.5rem;
    color: #666;
}

.modal-content input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 1rem;
    transition: border-color 0.3s;
}

.modal-content input:focus {
    outline: none;
    border-color: #667eea;
}

.modal-content button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s;
    width: 100%;
}

.modal-content button:hover {
    transform: translateY(-2px);
}

/* Header Styles */
.game-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.game-header h1 {
    font-size: 1.8rem;
    font-weight: 500;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

/* Game Container */
.game-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* Dice Section */
.dice-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.dice-section h2 {
    color: #667eea;
    margin-bottom: 2rem;
    font-size: 1.8rem;
}

.dice-container {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dice {
    width: 80px;
    height: 80px;
    background: white;
    border: 3px solid #667eea;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: #667eea;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s;
}

.dice.rolling {
    animation: diceRoll 0.6s ease-in-out;
}

@keyframes diceRoll {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(90deg); }
    50% { transform: rotate(180deg); }
    75% { transform: rotate(270deg); }
}

.score-display {
    margin-bottom: 2rem;
}

.total-score {
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
}

.total-score span:last-child {
    color: #667eea;
    font-weight: 700;
    font-size: 2rem;
}

.roll-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.roll-button:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.roll-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.roll-result {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #e8f5e8;
    border-radius: 10px;
    border-left: 4px solid #4caf50;
}

.roll-result.success {
    background: #e8f5e8;
    border-left-color: #4caf50;
    color: #2e7d32;
}

.roll-result.error {
    background: #ffebee;
    border-left-color: #f44336;
    color: #c62828;
}

/* Rankings Section */
.rankings-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    height: fit-content;
}

.rankings-section h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.3rem;
}

.rankings-list {
    max-height: 400px;
    overflow-y: auto;
}

.ranking-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s;
}

.ranking-item:hover {
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.ranking-item.highlighted {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    font-weight: 500;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    to { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
}

.rank-number {
    background: #667eea;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 500;
}

.ranking-item.highlighted .rank-number {
    background: #ff6b35;
}

.player-name {
    flex: 1;
    margin-left: 0.8rem;
    font-weight: 500;
}

.player-score {
    font-weight: 700;
    color: #667eea;
}

.ranking-item.highlighted .player-score {
    color: #ff6b35;
}

.highlight-info {
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    font-size: 0.9rem;
    color: #666;
}

/* History Section */
.history-section {
    grid-column: 1 / -1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-top: 1rem;
}

.history-section h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 1.3rem;
}

.history-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    transition: transform 0.2s;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.history-player {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
}

.history-dice {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.history-dice-value {
    background: #667eea;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 500;
}

.history-score {
    font-weight: 700;
    color: #667eea;
}

.history-time {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Loading Styles */
.loading {
    text-align: center;
    color: #666;
    padding: 2rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Messages */
.error-message {
    color: #f44336;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-container {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .game-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .dice-container {
        gap: 1rem;
    }
    
    .dice {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .history-list {
        grid-template-columns: 1fr;
    }
}

